/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly MODE: string;
  readonly BASE_URL: string;
  readonly PROD: boolean;
  readonly DEV: boolean;
  readonly SSR: boolean;
  // 自定义环境变量
  readonly VITE_API_URL?: string;
  readonly REACT_APP_API_URL?: string;
  readonly REACT_APP_COLLABORATION_SERVER_URL?: string;
  readonly REACT_APP_ENABLE_DEBUG?: string;
  readonly REACT_APP_LOG_LEVEL?: string;
  readonly REACT_APP_ENABLE_ANALYTICS?: string;
  readonly REACT_APP_ENABLE_MOCK_DATA?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// 扩展 Window 接口以支持环境变量注入
declare global {
  interface Window {
    __ENV__?: {
      NODE_ENV?: string;
      REACT_APP_API_URL?: string;
      REACT_APP_COLLABORATION_SERVER_URL?: string;
      REACT_APP_ENABLE_DEBUG?: string;
      REACT_APP_LOG_LEVEL?: string;
      REACT_APP_ENABLE_ANALYTICS?: string;
      REACT_APP_ENABLE_MOCK_DATA?: string;
    };
  }
}

export {};
