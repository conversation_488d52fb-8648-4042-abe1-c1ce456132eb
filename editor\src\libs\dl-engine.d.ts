/**
 * DL（Digital Learning）引擎类型声明文件
 * 自动生成于 2025-08-27T00:36:37.570Z
 * 包含 991 个类型定义
 */

// 外部依赖类型
/// <reference types="three" />
/// <reference types="cannon-es" />

// 已移除自定义 ES2015+ 全局类型声明，避免与 TS 标准库冲突（Iterator/IterableIterator/Iterator/IteratorResult/Generator 等）。
// 使用 TypeScript 标准库提供的类型定义


// CANNON.js 命名空间声明
declare namespace CANNON {
  export class Vec3 {
    x: number;
    y: number;
    z: number;
    constructor(x?: number, y?: number, z?: number);
  }

  export class Quaternion {
    x: number;
    y: number;
    z: number;
    w: number;
    constructor(x?: number, y?: number, z?: number, w?: number);
  }

  export class Material {
    name: string;
    id: number;
    friction: number;
    restitution: number;
    constructor(name?: string);
  }

  export class ContactMaterial {
    materials: Material[];
    friction: number;
    restitution: number;
    constructor(m1: Material, m2: Material, options?: any);
  }

  export class Shape {
    id: number;
    type: number;
    constructor();
  }

  export class Body {
    id: number;
    position: Vec3;
    quaternion: Quaternion;
    velocity: Vec3;
    angularVelocity: Vec3;
    mass: number;
    material: Material;
    shapes: Shape[];
    constructor(options?: any);
  }

  export class AABB {
    lowerBound: Vec3;
    upperBound: Vec3;
    constructor(options?: any);
  }
}

// 基础类型定义
type Primitive = string | number | boolean | null | undefined;
type AnyFunction = (...args: any[]) => any;
type AnyObject = Record<string, any>;

// 基础向量类型
export interface Vector2 {
  x: number;
  y: number;
}

export interface Vector3 {
  x: number;
  y: number;
  z: number;
}

export interface Quaternion {
  x: number;
  y: number;
  z: number;
  w: number;
}

export interface Color {
  r: number;
  g: number;
  b: number;
  a?: number;
}

// 基础类型别名
export type Hand = 'left' | 'right';
export type BlendMode = 'normal' | 'add' | 'multiply' | 'screen' | 'overlay';
export type LoopMode = 'once' | 'loop' | 'pingpong';
export type EasingType = 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out';
export type TransitionType = 'fade' | 'slide' | 'scale' | 'rotate';
export type InterpolationType = 'linear' | 'cubic' | 'step';
export type GrabType = 'physics' | 'kinematic' | 'snap';
export type ThrowType = 'velocity' | 'impulse' | 'force';
export type CameraType = 'perspective' | 'orthographic';
export type LightType = 'directional' | 'point' | 'spot' | 'area';
export type MaterialType = 'standard' | 'physical' | 'unlit' | 'toon';
export type SkyboxType = 'cubemap' | 'hdri' | 'procedural';
export type SceneFormat = 'gltf' | 'fbx' | 'obj' | 'dae';
export type AudioType = 'music' | 'sfx' | 'voice' | 'ambient';
export type InputActionType = 'button' | 'axis' | 'vector2' | 'vector3';
export type DeviceType = 'keyboard' | 'mouse' | 'gamepad' | 'touch' | 'xr';
export type NetworkProtocol = 'websocket' | 'webrtc' | 'udp' | 'tcp';
export type CompressionType = 'none' | 'gzip' | 'lz4' | 'brotli';
export type QuantizationBits = 8 | 16 | 32;
export type ExpressionStyle = 'realistic' | 'cartoon' | 'anime' | 'stylized';
export type DigitalHumanCreationType = 'photo' | 'manual' | 'ai-generated' | 'template';
export type InteractionCallback = (entity: any, data?: any) => void;
export type ConstraintType = 'fixed' | 'hinge' | 'slider' | 'spring' | 'distance';
export type EventHandler = (event: any) => void;
export type EventFilter = (event: any) => boolean;
export type AnimationTargetProperty = 'position' | 'rotation' | 'scale' | 'opacity' | 'color';
export type UIEasingFunction = (t: number) => number;
export type NodeConstructor = new (...args: any[]) => any;
export type ValueTypeCreator = () => any;
export type DLEventListener = (event: any) => void;

// 枚举类型将通过解析过程自动添加





// 缺失的基础枚举类型
export enum AIModelType {
  BERT = 'bert',
  GPT = 'gpt',
  TRANSFORMER = 'transformer',
  CNN = 'cnn',
  RNN = 'rnn'
}
export enum LayoutAlgorithm {
  GRID = 'grid',
  FLOW = 'flow',
  TREE = 'tree',
  FORCE_DIRECTED = 'force_directed'
}
export enum SceneElementType {
  MESH = 'mesh',
  LIGHT = 'light',
  CAMERA = 'camera',
  GROUP = 'group'
}
export interface Euler {
  x: number;
  y: number;
  z: number;
  order?: string;
}
export enum SpatialRelationType {
  CONTAINS = 'contains',
  INTERSECTS = 'intersects',
  ADJACENT = 'adjacent',
  OVERLAPS = 'overlaps'
}
export enum FacialExpressionType {
  NEUTRAL = 'neutral',
  HAPPY = 'happy',
  SAD = 'sad',
  ANGRY = 'angry',
  SURPRISED = 'surprised',
  FEAR = 'fear',
  DISGUST = 'disgust',
  CONTEMPT = 'contempt'
}
export enum VisemeType {
  SIL = 'sil',
  PP = 'pp',
  FF = 'ff',
  TH = 'th',
  DD = 'dd',
  KK = 'kk',
  CH = 'ch',
  SS = 'ss',
  NN = 'nn',
  RR = 'rr',
  AA = 'aa',
  E = 'e',
  I = 'i',
  O = 'o',
  U = 'u'
}
export enum FacialAnimationModelType {
  BLENDSHAPE = 'blendshape',
  BONE_BASED = 'bone_based',
  MUSCLE_BASED = 'muscle_based',
  HYBRID = 'hybrid'
}
export enum TrackType {
  POSITION = 'position',
  ROTATION = 'rotation',
  SCALE = 'scale',
  MORPH = 'morph'
}
export enum AnimationEventType {
  START = 'start',
  END = 'end',
  LOOP = 'loop',
  PAUSE = 'pause',
  RESUME = 'resume',
  STOP = 'stop'
}
export enum MaskType {
  BONE = 'bone',
  VERTEX = 'vertex',
  REGION = 'region',
  WEIGHT = 'weight'
}
export enum MaskWeightType {
  LINEAR = 'linear',
  SMOOTH = 'smooth',
  CUSTOM = 'custom',
  AUTOMATIC = 'automatic'
}
export enum DynamicMaskType {
  ADAPTIVE = 'adaptive',
  PROCEDURAL = 'procedural',
  AI_DRIVEN = 'ai_driven',
  PHYSICS_BASED = 'physics_based'
}
export enum OptimizationLevel {
  NONE = 0,
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  MAXIMUM = 4
}
export enum DebugEventType {
  BREAKPOINT = 'breakpoint',
  STEP = 'step',
  VARIABLE_CHANGE = 'variable_change',
  PERFORMANCE = 'performance'
}
export interface AnimationComponent {
  clips: any[];
  currentClip?: string;
  isPlaying: boolean;
}
export interface SkinnedMeshComponent {
  mesh: any;
  skeleton: any;
  bindMatrix: any;
}
export enum PhysicsObjectType {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  KINEMATIC = 'kinematic',
  TRIGGER = 'trigger'
}
export enum PhysicsConstraintType {
  FIXED = 'fixed',
  HINGE = 'hinge',
  SLIDER = 'slider',
  SPRING = 'spring'
}
export enum SoftBodyType {
  CLOTH = 'cloth',
  ROPE = 'rope',
  VOLUME = 'volume',
  SURFACE = 'surface'
}
export enum MuscleType {
  FACIAL = 'facial',
  SKELETAL = 'skeletal',
  CARDIAC = 'cardiac',
  SMOOTH = 'smooth'
}
export interface CannonPhysicsEngine {
  world: any;
  step: (deltaTime: number) => void;
}
export enum EventTriggerType {
  TIME = 'time',
  DISTANCE = 'distance',
  COLLISION = 'collision',
  USER_INPUT = 'user_input'
}
export enum ModifierType {
  SCALE = 'scale',
  OFFSET = 'offset',
  ROTATION = 'rotation',
  BLEND = 'blend'
}
export interface AnimationSubClip {
  name: string;
  startTime: number;
  endTime: number;
  tracks: any[];
}
export enum AssetType {
  TEXTURE = 'texture',
  MODEL = 'model',
  AUDIO = 'audio',
  VIDEO = 'video',
  ANIMATION = 'animation',
  MATERIAL = 'material',
  SHADER = 'shader',
  SCRIPT = 'script'
}
export enum DependencyType {
  REQUIRED = 'required',
  OPTIONAL = 'optional',
  WEAK = 'weak',
  CIRCULAR = 'circular'
}
export enum ResourceState {
  PENDING = 'pending',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error',
  CACHED = 'cached'
}
export interface EnhancedResourceManager {
  load: (url: string) => Promise<any>;
  unload: (url: string) => void;
}
export interface EnhancedResourceDependencyManager {
  addDependency: (parent: string, child: string) => void;
  resolveDependencies: (resource: string) => string[];
}
export interface AssetManager {
  load: (url: string) => Promise<any>;
  get: (url: string) => any;
}
export enum ChineseTokenizerType {
  JIEBA = 'jieba',
  PKUSEG = 'pkuseg',
  THULAC = 'thulac',
  HANLP = 'hanlp'
}
export enum ChineseDialectType {
  MANDARIN = 'mandarin',
  CANTONESE = 'cantonese',
  SHANGHAINESE = 'shanghainese',
  TAIWANESE = 'taiwanese'
}
export enum LandmarkType {
  FACE = 'face',
  HAND = 'hand',
  POSE = 'pose',
  HOLISTIC = 'holistic'
}
export enum SupportedLanguage {
  ENGLISH = 'en',
  CHINESE = 'zh',
  JAPANESE = 'ja',
  KOREAN = 'ko',
  SPANISH = 'es',
  FRENCH = 'fr'
}
export interface Texture {
  image: any;
  format: string;
  type: string;
}
export enum ActionType {
  MOVE = 'move',
  ROTATE = 'rotate',
  SCALE = 'scale',
  ANIMATE = 'animate',
  INTERACT = 'interact'
}
export enum ActionPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}
export enum ExpressionBlendMode {
  REPLACE = 'replace',
  ADD = 'add',
  MULTIPLY = 'multiply',
  OVERLAY = 'overlay'
}
export enum ExpressionPresetType {
  BASIC = 'basic',
  ADVANCED = 'advanced',
  CUSTOM = 'custom',
  REALISTIC = 'realistic'
}
export enum ExpressionIntensity {
  SUBTLE = 'subtle',
  MODERATE = 'moderate',
  STRONG = 'strong',
  EXTREME = 'extreme'
}
export enum StandardBoneType {
  ROOT = 'root',
  HIPS = 'hips',
  SPINE = 'spine',
  CHEST = 'chest',
  NECK = 'neck',
  HEAD = 'head',
  LEFT_SHOULDER = 'left_shoulder',
  LEFT_ARM = 'left_arm',
  LEFT_FOREARM = 'left_forearm',
  LEFT_HAND = 'left_hand',
  RIGHT_SHOULDER = 'right_shoulder',
  RIGHT_ARM = 'right_arm',
  RIGHT_FOREARM = 'right_forearm',
  RIGHT_HAND = 'right_hand',
  LEFT_THIGH = 'left_thigh',
  LEFT_SHIN = 'left_shin',
  LEFT_FOOT = 'left_foot',
  RIGHT_THIGH = 'right_thigh',
  RIGHT_SHIN = 'right_shin',
  RIGHT_FOOT = 'right_foot'
}
export enum ActionConflictType {
  BONE_CONFLICT = 'bone_conflict',
  TIME_CONFLICT = 'time_conflict',
  PRIORITY_CONFLICT = 'priority_conflict',
  RESOURCE_CONFLICT = 'resource_conflict'
}
export enum ConflictResolutionType {
  PRIORITY_BASED = 'priority_based',
  TIME_BASED = 'time_based',
  BLEND = 'blend',
  QUEUE = 'queue'
}
export enum ComparisonType {
  EQUAL = 'equal',
  NOT_EQUAL = 'not_equal',
  GREATER = 'greater',
  LESS = 'less',
  GREATER_EQUAL = 'greater_equal',
  LESS_EQUAL = 'less_equal'
}
export enum BIPBoneType {
  BIPED_ROOT = 'biped_root',
  BIPED_PELVIS = 'biped_pelvis',
  BIPED_SPINE = 'biped_spine',
  BIPED_SPINE1 = 'biped_spine1',
  BIPED_SPINE2 = 'biped_spine2',
  BIPED_SPINE3 = 'biped_spine3',
  BIPED_NECK = 'biped_neck',
  BIPED_HEAD = 'biped_head'
}
export enum ClothingCategory {
  TOP = 'top',
  BOTTOM = 'bottom',
  SHOES = 'shoes',
  ACCESSORIES = 'accessories',
  OUTERWEAR = 'outerwear'
}
export enum ClothingSlotType {
  HEAD = 'head',
  TORSO = 'torso',
  LEGS = 'legs',
  FEET = 'feet',
  HANDS = 'hands',
  ACCESSORIES = 'accessories'
}
export enum ClothingMaterialType {
  COTTON = 'cotton',
  LEATHER = 'leather',
  SILK = 'silk',
  WOOL = 'wool',
  SYNTHETIC = 'synthetic'
}
export interface Mesh {
  geometry: any;
  material: any;
  position: Vector3;
}
export enum AnimationGraphNodeType {
  CLIP = 'clip',
  BLEND = 'blend',
  STATE = 'state',
  TRANSITION = 'transition',
  CONDITION = 'condition'
}
export enum AvatarType {
  HUMANOID = 'humanoid',
  CREATURE = 'creature',
  ROBOT = 'robot',
  FANTASY = 'fantasy'
}
export enum DigitalHumanSource {
  PHOTO = 'photo',
  SCAN = 'scan',
  MANUAL = 'manual',
  AI_GENERATED = 'ai_generated'
}
export enum ControllerPresetType {
  FIRST_PERSON = 'first_person',
  THIRD_PERSON = 'third_person',
  FLYING = 'flying',
  VEHICLE = 'vehicle'
}
export enum SupportedFileFormat {
  GLTF = 'gltf',
  FBX = 'fbx',
  OBJ = 'obj',
  DAE = 'dae',
  BIP = 'bip',
  VRM = 'vrm'
}
export enum EnvironmentType {
  INDOOR = 'indoor',
  OUTDOOR = 'outdoor',
  STUDIO = 'studio',
  VIRTUAL = 'virtual'
}
export enum BehaviorType {
  IDLE = 'idle',
  WALKING = 'walking',
  TALKING = 'talking',
  GESTURING = 'gesturing',
  CUSTOM = 'custom'
}
export enum FacialAnimationPresetType {
  NEUTRAL = 'neutral',
  HAPPY = 'happy',
  SAD = 'sad',
  ANGRY = 'angry',
  SURPRISED = 'surprised'
}
export enum EmotionModelType {
  BASIC = 'basic',
  ADVANCED = 'advanced',
  NEURAL = 'neural',
  RULE_BASED = 'rule_based'
}
export enum EmotionModelVariant {
  STANDARD = 'standard',
  ENHANCED = 'enhanced',
  LITE = 'lite',
  CUSTOM = 'custom'
}
export enum EmotionEventType {
  TRIGGER = 'trigger',
  RESPONSE = 'response',
  TRANSITION = 'transition',
  COMPLETION = 'completion'
}
export enum VersionType {
  MAJOR = 'major',
  MINOR = 'minor',
  PATCH = 'patch',
  BETA = 'beta'
}
export enum VersionStatus {
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  STABLE = 'stable',
  DEPRECATED = 'deprecated'
}
export enum ChangeType {
  ADDED = 'added',
  MODIFIED = 'modified',
  REMOVED = 'removed',
  FIXED = 'fixed'
}
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}
export enum WeatherType {
  SUNNY = 'sunny',
  CLOUDY = 'cloudy',
  RAINY = 'rainy',
  SNOWY = 'snowy',
  FOGGY = 'foggy'
}
export enum TerrainType {
  FLAT = 'flat',
  HILLS = 'hills',
  MOUNTAINS = 'mountains',
  DESERT = 'desert',
  FOREST = 'forest'
}
export enum ResponsePriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  URGENT = 3
}
export interface Group {
  children: any[];
  add: (object: any) => void;
  remove: (object: any) => void;
}
export enum GestureType {
  TAP = 'tap',
  DOUBLE_TAP = 'double_tap',
  LONG_PRESS = 'long_press',
  SWIPE = 'swipe',
  PINCH = 'pinch',
  ROTATE = 'rotate',
  PAN = 'pan'
}
export enum GestureState {
  POSSIBLE = 'possible',
  BEGAN = 'began',
  CHANGED = 'changed',
  ENDED = 'ended',
  CANCELLED = 'cancelled',
  FAILED = 'failed'
}
export enum GestureDirection {
  UP = 'up',
  DOWN = 'down',
  LEFT = 'left',
  RIGHT = 'right',
  NONE = 'none'
}
export enum InputMappingType {
  BUTTON = 'button',
  AXIS = 'axis',
  VECTOR2 = 'vector2',
  VECTOR3 = 'vector3'
}
export interface DeviceCapabilities {
  maxTextureSize: number;
  maxVertexCount: number;
  supportsInstancing: boolean;
  supportsComputeShaders: boolean;
  memoryLimit: number;
}
export enum GrabNetworkEventType {
  GRAB_START = 'grab_start',
  GRAB_END = 'grab_end',
  GRAB_UPDATE = 'grab_update',
  OWNERSHIP_CHANGE = 'ownership_change'
}
export enum InteractionType {
  CLICK = 'click',
  HOVER = 'hover',
  GRAB = 'grab',
  TOUCH = 'touch',
  VOICE = 'voice'
}
export enum InteractionEventType {
  HOVER_START = 'hover_start',
  HOVER_END = 'hover_end',
  CLICK = 'click',
  GRAB = 'grab',
  RELEASE = 'release',
  FOCUS = 'focus',
  BLUR = 'blur'
}
export enum HighlightType {
  OUTLINE = 'outline',
  GLOW = 'glow',
  COLOR_CHANGE = 'color_change'
}
export enum PromptPositionType {
  ABOVE = 'above',
  BELOW = 'below',
  LEFT = 'left',
  RIGHT = 'right'
}
export enum BodyType {
  STATIC = 'static',
  DYNAMIC = 'dynamic',
  KINEMATIC = 'kinematic'
}
export enum XRControllerType {
  LEFT_HAND = 'left_hand',
  RIGHT_HAND = 'right_hand',
  GAMEPAD = 'gamepad'
}
export enum DataPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}
export enum BandwidthAllocationMode {
  EQUAL = 'equal',
  PRIORITY = 'priority',
  ADAPTIVE = 'adaptive'
}
export enum BandwidthControlStrategy {
  THROTTLE = 'throttle',
  QUEUE = 'queue',
  DROP = 'drop'
}
export enum NetworkEntityType {
  PLAYER = 'player',
  NPC = 'npc',
  OBJECT = 'object'
}
export enum NetworkEntitySyncMode {
  FULL = 'full',
  DELTA = 'delta',
  INTERPOLATED = 'interpolated'
}
export enum NetworkEntityOwnershipMode {
  SERVER = 'server',
  CLIENT = 'client',
  SHARED = 'shared'
}
export enum NetworkUserState {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTING = 'disconnecting',
  DISCONNECTED = 'disconnected'
}
export enum NetworkUserRole {
  GUEST = 'guest',
  USER = 'user',
  MODERATOR = 'moderator',
  ADMIN = 'admin'
}
export enum CompressionAlgorithm {
  NONE = 'none',
  GZIP = 'gzip',
  LZ4 = 'lz4',
  BROTLI = 'brotli'
}
export enum CompressionLevel {
  NONE = 0,
  FAST = 1,
  NORMAL = 5,
  BEST = 9
}
export enum UserRole {
  GUEST = 'guest',
  USER = 'user',
  MODERATOR = 'moderator',
  ADMIN = 'admin'
}
export enum PredictionAlgorithm {
  LINEAR = 'linear',
  QUADRATIC = 'quadratic',
  CUBIC = 'cubic'
}
export enum AdaptiveStrategy {
  CONSERVATIVE = 'conservative',
  AGGRESSIVE = 'aggressive',
  BALANCED = 'balanced'
}
export enum SyncAreaType {
  SPHERE = 'sphere',
  BOX = 'box',
  CUSTOM = 'custom'
}
export enum MediaStreamQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  ULTRA = 'ultra'
}
export enum MediaStreamType {
  AUDIO = 'audio',
  VIDEO = 'video',
  SCREEN = 'screen'
}
export enum ServiceDiscoveryClient {
  CONSUL = 'consul',
  ETCD = 'etcd',
  ZOOKEEPER = 'zookeeper'
}
export enum NetworkIssueType {
  LATENCY = 'latency',
  PACKET_LOSS = 'packet_loss',
  BANDWIDTH = 'bandwidth'
}
export enum NetworkQualityLevel {
  POOR = 'poor',
  FAIR = 'fair',
  GOOD = 'good',
  EXCELLENT = 'excellent'
}
export enum EncryptionAlgorithm {
  AES = 'aes',
  RSA = 'rsa',
  ECDSA = 'ecdsa'
}
export enum HashAlgorithm {
  MD5 = 'md5',
  SHA1 = 'sha1',
  SHA256 = 'sha256'
}
export enum NetworkProtocolState {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTING = 'disconnecting'
}
export enum EmitterShapeType {
  POINT = 'point',
  SPHERE = 'sphere',
  BOX = 'box',
  CONE = 'cone',
  CYLINDER = 'cylinder'
}
export enum CollisionEventType {
  ENTER = 'enter',
  EXIT = 'exit',
  STAY = 'stay'
}
export enum ColliderType {
  BOX = 'box',
  SPHERE = 'sphere',
  CAPSULE = 'capsule',
  CYLINDER = 'cylinder',
  PLANE = 'plane',
  MESH = 'mesh'
}
export interface PhysicsSystem {
  step: (deltaTime: number) => void;
  addBody: (body: any) => void;
}
export enum VesselConnectionType {
  ARTERY = 'artery',
  VEIN = 'vein',
  CAPILLARY = 'capillary'
}
export enum OrganType {
  HEART = 'heart',
  LUNG = 'lung',
  LIVER = 'liver',
  KIDNEY = 'kidney',
  BRAIN = 'brain'
}
export interface VesselComponent {
  type: string;
  diameter: number;
  flow: number;
}
export enum FountainJetShape {
  STRAIGHT = 'straight',
  CURVED = 'curved',
  SPIRAL = 'spiral',
  FAN = 'fan'
}
export enum EnhancedFountainType {
  MUSICAL = 'musical',
  DANCING = 'dancing',
  INTERACTIVE = 'interactive',
  PROGRAMMABLE = 'programmable'
}
export enum RaindropShape {
  SPHERE = 'sphere',
  TEARDROP = 'teardrop',
  ELONGATED = 'elongated'
}
export enum EnhancedRainWaterType {
  ACID_RAIN = 'acid_rain',
  SNOW = 'snow',
  HAIL = 'hail',
  SLEET = 'sleet'
}
export enum FountainType {
  SIMPLE = 'simple',
  TIERED = 'tiered',
  GEYSER = 'geyser',
  DECORATIVE = 'decorative'
}
export enum FountainMode {
  CONTINUOUS = 'continuous',
  INTERMITTENT = 'intermittent',
  TIMED = 'timed'
}
export enum FountainPresetType {
  CLASSIC = 'classic',
  MODERN = 'modern',
  NATURAL = 'natural',
  ARTISTIC = 'artistic'
}
export enum HotSpringType {
  NATURAL = 'natural',
  ARTIFICIAL = 'artificial',
  GEOTHERMAL = 'geothermal'
}
export enum LakeShapeType {
  CIRCULAR = 'circular',
  OVAL = 'oval',
  IRREGULAR = 'irregular',
  KIDNEY = 'kidney'
}
export enum OceanWaveType {
  CALM = 'calm',
  MODERATE = 'moderate',
  ROUGH = 'rough',
  STORM = 'storm'
}
export enum RainWaterType {
  LIGHT = 'light',
  MODERATE = 'moderate',
  HEAVY = 'heavy',
  STORM = 'storm'
}
export enum RainWaterPresetType {
  DRIZZLE = 'drizzle',
  SHOWER = 'shower',
  DOWNPOUR = 'downpour',
  THUNDERSTORM = 'thunderstorm'
}
export enum WaterBodyType {
  LAKE = 'lake',
  RIVER = 'river',
  OCEAN = 'ocean',
  POND = 'pond'
}
export enum WaterBodyShape {
  NATURAL = 'natural',
  GEOMETRIC = 'geometric',
  CUSTOM = 'custom'
}
export enum WaterfallType {
  CASCADE = 'cascade',
  PLUNGE = 'plunge',
  TIERED = 'tiered',
  CURTAIN = 'curtain'
}
export enum WaterfallPresetType {
  NIAGARA = 'niagara',
  ANGEL_FALLS = 'angel_falls',
  VICTORIA = 'victoria',
  IGUAZU = 'iguazu'
}
export enum WaterPresetType {
  CLEAR = 'clear',
  MURKY = 'murky',
  TROPICAL = 'tropical',
  ARCTIC = 'arctic'
}
export interface WaterBodyComponent {
  type: string;
  volume: number;
  temperature: number;
}
export enum DevicePerformanceLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  ULTRA = 'ultra'
}
export enum IntentType {
  QUESTION = 'question',
  COMMAND = 'command',
  GREETING = 'greeting',
  FAREWELL = 'farewell'
}
export enum EmotionType {
  JOY = 'joy',
  SADNESS = 'sadness',
  ANGER = 'anger',
  FEAR = 'fear',
  SURPRISE = 'surprise',
  DISGUST = 'disgust',
  NEUTRAL = 'neutral'
}
export enum NavigationState {
  IDLE = 'idle',
  MOVING = 'moving',
  PAUSED = 'paused',
  COMPLETED = 'completed'
}
export interface StopPoint {
  id: string;
  position: Vector3;
  name?: string;
  description?: string;
}
export enum RetrievalStrategy {
  DENSE = 'dense',
  SPARSE = 'sparse',
  HYBRID = 'hybrid'
}
export enum QueryType {
  SEMANTIC = 'semantic',
  KEYWORD = 'keyword',
  HYBRID = 'hybrid'
}
export enum LightShaftType {
  VOLUMETRIC = 'volumetric',
  GOD_RAYS = 'god_rays',
  ATMOSPHERIC = 'atmospheric'
}
export enum AreaLightType {
  RECT = 'rect',
  DISK = 'disk',
  SPHERE = 'sphere',
  TUBE = 'tube'
}
export enum IESLightType {
  TYPE_A = 'type_a',
  TYPE_B = 'type_b',
  TYPE_C = 'type_c'
}
export enum SimplificationAlgorithm {
  QUADRIC = 'quadric',
  EDGE_COLLAPSE = 'edge_collapse',
  VERTEX_CLUSTERING = 'vertex_clustering'
}
export enum LODLevel {
  LEVEL_0 = 0,
  LEVEL_1 = 1,
  LEVEL_2 = 2,
  LEVEL_3 = 3,
  LEVEL_4 = 4
}
export enum OcclusionCullingAlgorithm {
  HIERARCHICAL_Z = 'hierarchical_z',
  OCCLUSION_QUERIES = 'occlusion_queries',
  SOFTWARE_RASTERIZATION = 'software_rasterization'
}
export enum SSAOOutputMode {
  OCCLUSION_ONLY = 'occlusion_only',
  MULTIPLY = 'multiply',
  SCREEN = 'screen'
}
export enum ToneMappingType {
  LINEAR = 'linear',
  REINHARD = 'reinhard',
  CINEON = 'cineon',
  ACES = 'aces'
}
export enum UnderwaterParticleType {
  BUBBLES = 'bubbles',
  SEDIMENT = 'sediment',
  PLANKTON = 'plankton',
  DEBRIS = 'debris'
}
export enum WaterEffectType {
  RIPPLES = 'ripples',
  WAVES = 'waves',
  FOAM = 'foam',
  CAUSTICS = 'caustics'
}
export enum ResourcePriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}
export interface Scene {
  name: string;
  entities: any[];
  add: (entity: any) => void;
}
export interface SceneSerializer {
  serialize: (scene: any) => string;
  deserialize: (data: string) => any;
}
export enum SceneLayerType {
  BACKGROUND = 'background',
  ENVIRONMENT = 'environment',
  OBJECTS = 'objects',
  UI = 'ui',
  EFFECTS = 'effects'
}
export enum ResourceType {
  TEXTURE = 'texture',
  MODEL = 'model',
  AUDIO = 'audio',
  ANIMATION = 'animation',
  MATERIAL = 'material'
}
export enum SceneTransitionType {
  FADE = 'fade',
  SLIDE = 'slide',
  DISSOLVE = 'dissolve',
  WIPE = 'wipe'
}
export interface World {
  entities: any[];
  systems: any[];
  addEntity: (entity: any) => void;
}
export enum SceneResourceType {
  GEOMETRY = 'geometry',
  TEXTURE = 'texture',
  MATERIAL = 'material',
  ANIMATION = 'animation'
}
export enum PreloadStrategyType {
  IMMEDIATE = 'immediate',
  LAZY = 'lazy',
  PROGRESSIVE = 'progressive'
}
export interface ScenePreloader {
  preload: (scene: string) => Promise<void>;
}
export enum SurgicalToolType {
  SCALPEL = 'scalpel',
  FORCEPS = 'forceps',
  SCISSORS = 'scissors',
  CLAMP = 'clamp'
}
export enum CameraPathType {
  LINEAR = 'linear',
  BEZIER = 'bezier',
  SPLINE = 'spline',
  CIRCULAR = 'circular'
}
export enum HeightMapFormat {
  PNG = 'png',
  TIFF = 'tiff',
  EXR = 'exr',
  RAW = 'raw'
}
export enum ThirdPartyTerrainFormat {
  WORLD_MACHINE = 'world_machine',
  GAEA = 'gaea',
  TERRAGEN = 'terragen',
  UNITY_TERRAIN = 'unity_terrain'
}
export enum CoordinateSystem {
  LEFT_HANDED = 'left_handed',
  RIGHT_HANDED = 'right_handed',
  Y_UP = 'y_up',
  Z_UP = 'z_up'
}
export enum UndergroundWaterType {
  AQUIFER = 'aquifer',
  SPRING = 'spring',
  UNDERGROUND_RIVER = 'underground_river',
  CAVE_POOL = 'cave_pool'
}
export enum TextureCompressionFormat {
  DXT1 = 'dxt1',
  DXT5 = 'dxt5',
  BC7 = 'bc7',
  ASTC = 'astc'
}
export enum TerrainFeatureType {
  MOUNTAIN = 'mountain',
  VALLEY = 'valley',
  PLATEAU = 'plateau',
  CANYON = 'canyon'
}
export enum BrushType {
  RAISE = 'raise',
  LOWER = 'lower',
  SMOOTH = 'smooth',
  FLATTEN = 'flatten'
}
export enum BrushShape {
  CIRCLE = 'circle',
  SQUARE = 'square',
  CUSTOM = 'custom'
}
export enum TerrainWorkerMessageType {
  GENERATE = 'generate',
  UPDATE = 'update',
  COMPLETE = 'complete',
  ERROR = 'error'
}
export enum UploadStatus {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  COMPLETED = 'completed',
  FAILED = 'failed'
}
export enum BillboardMode {
  NONE = 'none',
  SCREEN_ALIGNED = 'screen_aligned',
  WORLD_ALIGNED = 'world_aligned',
  AXIS_ALIGNED = 'axis_aligned'
}
export enum UIComponentType {
  BUTTON = 'button',
  PANEL = 'panel',
  TEXT = 'text',
  IMAGE = 'image'
}
export enum UILayoutType {
  ABSOLUTE = 'absolute',
  RELATIVE = 'relative',
  FLEX = 'flex',
  GRID = 'grid'
}
export enum UIEventType {
  CLICK = 'click',
  HOVER = 'hover',
  FOCUS = 'focus',
  BLUR = 'blur',
  DRAG = 'drag',
  DROP = 'drop',
  RESIZE = 'resize'
}
export interface UIComponent {
  type: string;
  visible: boolean;
  position: Vector3;
}
export enum UIAnimationType {
  FADE = 'fade',
  SLIDE = 'slide',
  SCALE = 'scale',
  ROTATE = 'rotate'
}
export enum BottleneckType {
  CPU = 'cpu',
  GPU = 'gpu',
  MEMORY = 'memory',
  NETWORK = 'network'
}
export enum PerformanceMetricType {
  FPS = 'fps',
  FRAME_TIME = 'frame_time',
  MEMORY_USAGE = 'memory_usage',
  GPU_USAGE = 'gpu_usage'
}
export enum PerformanceBottleneckType {
  DRAW_CALLS = 'draw_calls',
  VERTEX_COUNT = 'vertex_count',
  TEXTURE_MEMORY = 'texture_memory',
  SHADER_COMPLEXITY = 'shader_complexity'
}
export enum PerformanceTrendType {
  IMPROVING = 'improving',
  STABLE = 'stable',
  DEGRADING = 'degrading'
}
export enum VegetationType {
  TREE = 'tree',
  BUSH = 'bush',
  GRASS = 'grass',
  FLOWER = 'flower'
}
export enum VegetationGrowthStage {
  SEED = 'seed',
  SPROUT = 'sprout',
  YOUNG = 'young',
  MATURE = 'mature',
  OLD = 'old'
}
export enum VegetationInteractionType {
  COMPETITION = 'competition',
  SYMBIOSIS = 'symbiosis',
  PARASITISM = 'parasitism',
  NEUTRAL = 'neutral'
}
export enum AquaticVegetationType {
  ALGAE = 'algae',
  SEAWEED = 'seaweed',
  WATER_LILY = 'water_lily',
  CORAL = 'coral'
}
export enum SeasonalVegetationType {
  DECIDUOUS = 'deciduous',
  EVERGREEN = 'evergreen',
  SEASONAL_FLOWER = 'seasonal_flower',
  SEASONAL_FRUIT = 'seasonal_fruit'
}
export enum WindZoneType {
  GLOBAL = 'global',
  LOCAL = 'local',
  TURBULENCE = 'turbulence'
}
export enum WindFieldType {
  UNIFORM = 'uniform',
  VORTEX = 'vortex',
  NOISE = 'noise',
  CUSTOM = 'custom'
}
export enum BreakpointType {
  LINE = 'line',
  CONDITIONAL = 'conditional',
  FUNCTION = 'function',
  EXCEPTION = 'exception'
}
export interface Graph {
  nodes: any[];
  edges: any[];
  addNode: (node: any) => void;
}
export interface VisualScriptEngine {
  execute: (graph: any) => void;
  compile: (graph: any) => any;
}
export enum SocketType {
  FLOW = 'flow',
  DATA = 'data',
  EVENT = 'event'
}
export enum SocketDirection {
  INPUT = 'input',
  OUTPUT = 'output'
}
export interface ExecutionContext {
  variables: Map<string, any>;
  stack: any[];
}
export enum NodeCategory {
  LOGIC = 'logic',
  MATH = 'math',
  EVENT = 'event',
  FLOW = 'flow',
  VARIABLE = 'variable'
}
export enum CacheStrategy {
  NONE = 'none',
  LRU = 'lru',
  FIFO = 'fifo',
  CUSTOM = 'custom'
}
export interface NodeRegistry {
  register: (type: string, constructor: any) => void;
  create: (type: string) => any;
}
export interface ValueTypeRegistry {
  register: (type: string, validator: any) => void;
  validate: (type: string, value: any) => boolean;
}
export enum NetworkEventType {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  MESSAGE = 'message',
  ERROR = 'error',
  DATA = 'data',
  GRAB = 'grab',
  RELEASE = 'release'
}
export enum EventPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}
export enum UserPermission {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  ADMIN = 'admin'
}
export enum TemplateParameterType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  OBJECT = 'object'
}
export enum SeasonType {
  SPRING = 'spring',
  SUMMER = 'summer',
  AUTUMN = 'autumn',
  WINTER = 'winter'
}
export enum FacialRegion {
  EYES = 'eyes',
  MOUTH = 'mouth',
  EYEBROWS = 'eyebrows',
  CHEEKS = 'cheeks',
  FOREHEAD = 'forehead'
}
export enum OptimizationType {
  LOD = 'lod',
  CULLING = 'culling',
  BATCHING = 'batching',
  COMPRESSION = 'compression'
}
export interface SubClip {
  name: string;
  startTime: number;
  endTime: number;
  tracks: any[];
  loop?: boolean;
}
export interface BIPKeyframe<T> {
  time: number;
  value: T;
  interpolation?: 'linear' | 'cubic' | 'step';
  easing?: string;
}
export interface InputBinding {
  action: string;
  key?: string;
  button?: number;
  axis?: number;
}
export interface SceneLayer {
  name: string;
  visible: boolean;
  entities: any[];
}
export interface Component {
  type: string;
  entity?: any;
}
export interface AnimationInstanceComponent {
  instanceId: string;
  transform: any;
  animation: any;
  isPlaying: boolean;
  currentTime: number;
}
export type SystemEventType = 'systemInitialized' | 'digitalHumanCreated' | 'digitalHumanLoaded' | 'digitalHumanUnloaded' | 'performanceWarning' | 'healthStatusChanged' | 'configUpdated' | 'syncCompleted' | 'optimizationApplied' | 'errorOccurred';

export interface Entity {
    text: string;
    type: string;
    confidence: number;
    startOffset: number;
    endOffset: number;
}

export interface InteractionEvent {
    type: string;
    data: any;
    timestamp: Date;
    source: 'voice' | 'gesture' | 'navigation' | 'system';
}

export type InteractionEventListener = (event: InteractionEvent) => void;

export interface NetworkEvent {
    
    type: NetworkEventType | string;
    
    data?: any;
    
    senderId?: string;
    
    receiverId?: string;
    
    timestamp: number;
    
    id?: string;
    
    priority?: number;
    
    handled?: boolean;
    
    result?: any;
    
    metadata?: Record<string, any>;
}

export type PathPointType = 'normal' | 'stop' | 'waypoint' | 'interaction';

export type EventListener = (event: UIEvent) => void;

export type EventCallback = (...args: any[]) => void;

export type ValueTypeValidator = (value: any) => boolean;

export type ValueTypeConverter = (value: any) => any;

export interface AIEmotionAnalysisSystemConfig {
    
    debug?: boolean;
    
    useLocalModel?: boolean;
    
    modelPath?: string;
}

export interface AIModelCacheConfig {
    
    maxSize?: number;
    
    expireTime?: number;
    
    debug?: boolean;
    
    cleanupInterval?: number;
    
    hitRateThreshold?: number;
    
    hitRateWindowSize?: number;
}

export interface AIModelConfig {
    
    version?: string;
    
    variant?: string;
    
    temperature?: number;
    
    maxTokens?: number;
    
    stream?: boolean;
    
    batchSize?: number;
    
    quantized?: boolean;
    
    quantizationBits?: 8 | 16 | 32;
    
    useGPU?: boolean;
    
    gpuDeviceId?: number;
    
    apiKey?: string;
    
    baseUrl?: string;
    
    modelPath?: string;
    
    useLocalModel?: boolean;
    
    cacheSize?: number;
    
    debug?: boolean;
    
    [key: string]: any;
}

export interface AIModelFactoryConfig {
    
    debug?: boolean;
    
    useLocalModels?: boolean;
    
    apiKeys?: Record<string, string>;
    
    baseUrls?: Record<string, string>;
    
    modelVersions?: Record<string, string>;
}

export interface AIModelLoadOptions {
    
    forceReload?: boolean;
    
    timeout?: number;
    
    priority?: number;
    
    onProgress?: (progress: number) => void;
    
    onComplete?: (success: boolean) => void;
    
    onError?: (error: Error) => void;
    
    useCache?: boolean;
    
    useOfflineModel?: boolean;
    
    useLowPrecision?: boolean;
    
    useFactory?: boolean;
    
    [key: string]: any;
}

export interface AIModelManagerConfig {
    
    debug?: boolean;
    
    cacheSize?: number;
    
    useLocalModels?: boolean;
    
    apiKeys?: Record<string, string>;
    
    baseUrls?: Record<string, string>;
    
    modelVersions?: Record<string, string>;
}

export interface BatchProcessorConfig {
    
    maxBatchSize?: number;
    
    maxWaitTime?: number;
    
    debug?: boolean;
    
    dynamicBatchSize?: boolean;
    
    minBatchSize?: number;
    
    performanceWindowSize?: number;
    
    batchTimeout?: number;
    
    maxQueueSize?: number;
}

export interface BatchPerformanceStats {
    
    batchCount: number;
    
    requestCount: number;
    
    averageBatchSize: number;
    
    averageProcessTime: number;
    
    averageWaitTime: number;
    
    queueSize: number;
    
    currentBatchSize: number;
    
    maxBatchSize: number;
    
    maxWaitTime: number;
}

export interface QuantizationConfig {
    
    enabled: boolean;
    
    bits: QuantizationBits;
    
    symmetric?: boolean;
    
    perChannel?: boolean;
    
    dynamic?: boolean;
    
    debug?: boolean;
}

export interface QuantizationStats {
    
    originalSize: number;
    
    quantizedSize: number;
    
    compressionRatio: number;
    
    quantizationTime: number;
    
    precisionLoss?: number;
}

export interface ALBERTModelConfig extends AIModelConfig {
    
    variant?: 'base' | 'large' | 'xlarge' | 'xxlarge';
    
    emotionCategories?: string[];
    
    confidenceThreshold?: number;
    
    maxSequenceLength?: number;
}

export interface BARTModelConfig extends AIModelConfig {
    
    variant?: 'base' | 'large' | 'cnn';
    
    minLength?: number;
    
    maxLength?: number;
    
    useBeamSearch?: boolean;
    
    beamSize?: number;
    
    useLengthPenalty?: boolean;
    
    lengthPenalty?: number;
}

export interface DistilBERTModelConfig extends AIModelConfig {
    
    variant?: 'base' | 'multilingual';
    
    emotionCategories?: string[];
    
    confidenceThreshold?: number;
    
    maxSequenceLength?: number;
}

export interface TextGenerationOptions {
    
    maxTokens?: number;
    
    temperature?: number;
    
    stream?: boolean;
    
    onStream?: (text: string) => void;
    
    stopSequences?: string[];
    
    samplingMethod?: 'greedy' | 'topk' | 'topp' | 'beam';
    
    topK?: number;
    
    topP?: number;
    
    beamWidth?: number;
    
    repetitionPenalty?: number;
    
    lengthPenalty?: number;
    
    [key: string]: any;
}

export interface ImageGenerationOptions {
    
    width?: number;
    
    height?: number;
    
    steps?: number;
    
    guidanceScale?: number;
    
    seed?: number;
    
    negativePrompt?: string;
    
    sampler?: string;
    
    safetyChecker?: boolean;
    
    onProgress?: (progress: number) => void;
    
    [key: string]: any;
}

export interface EmotionAnalysisResult {
    
    primaryEmotion: string;
    
    intensity: number;
    
    scores: Record<string, number>;
    
    confidence: number;
}

export interface TextClassificationResult {
    
    label: string;
    
    confidence: number;
    
    allLabels: Record<string, number>;
}

export interface NamedEntityRecognitionResult {
    
    entities: Array<{
        
        text: string;
        
        type: string;
        
        start: number;
        
        end: number;
        
        confidence: number;
    }>;
}

export interface TextSummaryResult {
    
    summary: string;
    
    length: number;
    
    compressionRate: number;
}

export interface TranslationResult {
    
    translatedText: string;
    
    sourceLanguage: string;
    
    targetLanguage: string;
    
    confidence: number;
}

export interface IAIModel {
    
    getType(): AIModelType;
    
    getConfig(): AIModelConfig;
    
    initialize(): Promise<boolean>;
    
    generateText(prompt: string, options?: TextGenerationOptions): Promise<string>;
    
    generateImage?(prompt: string, options?: ImageGenerationOptions): Promise<Blob>;
    
    analyzeEmotion?(text: string): Promise<EmotionAnalysisResult>;
    
    classifyText?(text: string, categories?: string[]): Promise<TextClassificationResult>;
    
    recognizeEntities?(text: string): Promise<NamedEntityRecognitionResult>;
    
    summarizeText?(text: string, maxLength?: number): Promise<TextSummaryResult>;
    
    translateText?(text: string, targetLanguage: string, sourceLanguage?: string): Promise<TranslationResult>;
    
    dispose(): void;
}

export interface RoBERTaModelConfig extends AIModelConfig {
    
    variant?: 'base' | 'large' | 'distilled';
    
    useMultiLabel?: boolean;
    
    emotionCategories?: string[];
    
    confidenceThreshold?: number;
}

export interface T5ModelConfig extends AIModelConfig {
    
    variant?: 'small' | 'base' | 'large' | 'xl' | 'xxl';
    
    minLength?: number;
    
    maxLength?: number;
    
    useBeamSearch?: boolean;
    
    beamSize?: number;
    
    earlyStoppingStrategy?: 'none' | 'length' | 'probability';
}

export interface XLNetModelConfig extends AIModelConfig {
    
    variant?: 'base' | 'large';
    
    emotionCategories?: string[];
    
    confidenceThreshold?: number;
    
    maxSequenceLength?: number;
    
    useMemoryCache?: boolean;
}

export interface Asset {
    
    id: string;
    
    name: string;
    
    type: string;
    
    category: string;
    
    description: string;
    
    tags: string[];
    
    url: string;
    
    metadata: {
        
        dimensions?: {
            width: number;
            height: number;
            depth: number;
        };
        
        style?: string;
        
        color?: string;
        
        material?: string;
        
        polygonCount?: number;
        
        textures?: string[];
    };
    
    embedding?: number[];
    
    confidence?: number;
}

export interface AssetMatchingConfig extends AIModelConfig {
    
    embeddingModel?: string;
    
    similarityThreshold?: number;
    
    maxResults?: number;
    
    enableStyleMatching?: boolean;
    
    enableSizeAdaptation?: boolean;
    
    enableProceduralGeneration?: boolean;
}

export interface SceneModification {
    
    type: 'add' | 'remove' | 'move' | 'resize' | 'recolor' | 'replace';
    
    target: string;
    
    parameters: Record<string, any>;
    
    priority: number;
}

export interface FeedbackAnalysis {
    
    type: 'modification' | 'addition' | 'removal' | 'style_change' | 'layout_change';
    
    targetObjects: string[];
    
    modifications: SceneModification[];
    
    confidence: number;
    
    sentiment: 'positive' | 'negative' | 'neutral';
}

export interface SceneOptimizationResult {
    
    optimizedScene: any;
    
    modifications: SceneModification[];
    
    confidence: number;
    
    explanation: string;
    
    suggestions: string[];
}

export interface ConversationMessage {
    
    id: string;
    
    type: 'user' | 'system';
    
    content: string;
    
    timestamp: number;
    
    relatedScene?: any;
    
    confidence?: number;
}

export interface ConversationContext {
    
    sessionId: string;
    
    messages: ConversationMessage[];
    
    currentScene: any;
    
    userPreferences: Record<string, any>;
    
    conversationState: 'initial' | 'active' | 'modifying' | 'completed';
}

export interface LayoutGenerationConfig extends AIModelConfig {
    
    algorithm?: LayoutAlgorithm;
    
    optimization?: string;
    
    maxIterations?: number;
    
    convergenceThreshold?: number;
    
    gridSize?: number;
    
    minSpacing?: number;
}

export interface NLPProcessorConfig {
    
    language?: string;
    
    enableNER?: boolean;
    
    enablePOS?: boolean;
    
    enableDependency?: boolean;
    
    enableSentiment?: boolean;
}

export interface SceneGenerationAIManagerConfig extends AIModelManagerConfig {
    
    sceneUnderstanding?: any;
    
    layoutGeneration?: any;
    
    assetMatching?: any;
    
    enableRealTimeGeneration?: boolean;
    
    qualityLevel?: 'fast' | 'balanced' | 'high';
}

export interface SceneElement {
    
    id?: string;
    
    type: SceneElementType;
    
    name: string;
    
    category: string;
    
    description?: string;
    
    attributes: Record<string, any>;
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    scale?: THREE.Vector3;
    
    requiredSize?: THREE.Vector3;
    
    materialHint?: string;
}

export interface SpatialRelation {
    
    type: SpatialRelationType;
    
    source: string;
    
    target: string;
    
    distance?: number;
    
    confidence: number;
}

export interface SceneIntent {
    
    sceneType: string;
    
    style: string;
    
    functionality: string[];
    
    mood?: string;
    
    timeOfDay?: string;
    
    weather?: string;
}

export interface SceneConstraint {
    
    type: 'physical' | 'aesthetic' | 'functional' | 'spatial';
    
    description: string;
    
    parameters: Record<string, any>;
    
    priority: number;
}

export interface SceneUnderstanding {
    
    elements: SceneElement[];
    
    spatialRelations: SpatialRelation[];
    
    intent: SceneIntent;
    
    constraints: SceneConstraint[];
    
    confidence: number;
}

export interface LayoutElement {
    
    element: SceneElement;
    
    position: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    scale: THREE.Vector3;
    
    boundingBox: THREE.Box3;
}

export interface EnvironmentConfig {
    
    skybox?: string;
    
    ambientColor: THREE.Color;
    
    ambientIntensity: number;
    
    mainLight?: {
        type: 'directional' | 'point' | 'spot';
        color: THREE.Color;
        intensity: number;
        position: THREE.Vector3;
        direction?: THREE.Vector3;
    };
    
    postProcessing?: Record<string, any>;
}

export interface SceneLayout {
    
    id: string;
    
    bounds: THREE.Box3;
    
    elements: LayoutElement[];
    
    environment: EnvironmentConfig;
    
    score?: number;
}

export interface AssetMatchResult {
    
    element: SceneElement;
    
    asset: any;
    
    confidence: number;
    
    source: 'database' | 'generated' | 'procedural';
}

export interface SceneGenerationOptions {
    
    realTimePreview?: boolean;
    
    voiceGuidance?: boolean;
    
    stylePreference?: string;
    
    complexityLevel?: 'simple' | 'medium' | 'complex';
    
    performancePriority?: 'quality' | 'speed' | 'balanced';
}

export interface SceneGenerationResult {
    
    scene: any;
    
    understanding: SceneUnderstanding;
    
    layout: SceneLayout;
    
    assets: AssetMatchResult[];
    
    confidence: number;
    
    generationTime?: number;
}

export interface SceneRequirements {
    
    sceneType: string;
    
    elements: SceneElement[];
    
    spatialRelations: SpatialRelation[];
    
    constraints: SceneConstraint[];
    
    style: string;
    
    scale: 'small' | 'medium' | 'large';
}

export interface NLPResult {
    
    tokens: string[];
    
    posTags: Array<{
        word: string;
        tag: string;
    }>;
    
    entities: Array<{
        text: string;
        label: string;
        start: number;
        end: number;
    }>;
    
    dependencies?: Array<{
        head: number;
        dep: string;
        tail: number;
    }>;
    
    sentiment?: {
        label: string;
        score: number;
    };
}

export interface PerformanceConfig {
    
    targetFPS: number;
    
    maxPolygons: number;
    
    maxTextureSize: number;
    
    enableLOD: boolean;
    
    enableGeometryMerging: boolean;
    
    enableTextureCompression: boolean;
    
    enableInstancing: boolean;
}

export interface PerformanceStats {
    
    currentFPS: number;
    
    polygonCount: number;
    
    textureMemory: number;
    
    drawCalls: number;
    
    suggestions: string[];
}

export interface SceneUnderstandingConfig extends AIModelConfig {
    
    modelType?: 'transformer' | 'bert' | 'gpt' | 'custom';
    
    language?: string;
    
    domain?: string;
    
    confidenceThreshold?: number;
    
    enableNER?: boolean;
    
    enablePOS?: boolean;
    
    enableDependency?: boolean;
}

export interface VoiceSessionResult {
    
    sessionId: string;
    
    status: 'active' | 'paused' | 'stopped' | 'error';
    
    error?: string;
}

export interface VoiceInteraction {
    
    timestamp: number;
    
    userInput: string;
    
    systemResponse: string;
    
    generationResult?: SceneGenerationResult;
    
    confidence: number;
}

export interface VoiceSessionState {
    
    sessionId: string;
    
    isListening: boolean;
    
    isGenerating: boolean;
    
    isSpeaking: boolean;
    
    currentTranscript: string;
    
    generationProgress: number;
    
    currentScene?: any;
    
    history: VoiceInteraction[];
}

export interface ExpressionMapping {
    
    expression: FacialExpressionType;
    
    blendShapeName: string;
    
    blendShapeIndex?: number;
    
    weightScale?: number;
    
    weightOffset?: number;
}

export interface VisemeMapping {
    
    viseme: VisemeType;
    
    blendShapeName: string;
    
    blendShapeIndex?: number;
    
    weightScale?: number;
    
    weightOffset?: number;
}

export interface FacialAnimationModelAdapterConfig {
    
    modelType?: FacialAnimationModelType;
    
    expressionMappings?: ExpressionMapping[];
    
    visemeMappings?: VisemeMapping[];
    
    autoDetectBlendShapes?: boolean;
    
    debug?: boolean;
}

export interface FacialAnimationModelAdapterSystemConfig {
    
    debug?: boolean;
    
    defaultModelType?: FacialAnimationModelType;
    
    autoDetectBlendShapes?: boolean;
}

export interface VRMFacialAnimationAdapterConfig {
    
    debug?: boolean;
    
    autoDetectBlendShapes?: boolean;
}

export interface Keyframe {
    
    time: number;
    
    value: any;
    
    interpolation?: InterpolationType;
    
    inTangent?: any;
    
    outTangent?: any;
}

export interface AnimationTrack {
    
    type: TrackType;
    
    targetPath: string;
    
    keyframes: Keyframe[];
    
    defaultInterpolation: InterpolationType;
}

export interface AnimationClip {
    
    id: string;
    
    name: string;
    
    duration: number;
    
    frameRate: number;
    
    tracks: AnimationTrack[];
    
    type: 'idle' | 'walk' | 'run' | 'gesture' | 'expression' | 'custom';
    
    priority: number;
    
    blendable: boolean;
    
    weight: number;
    
    metadata: {
        source?: string;
        author?: string;
        tags?: string[];
        description?: string;
    };
}

export interface FacialAnimationKeyframe {
    
    time: number;
    
    expression?: FacialExpressionType;
    
    expressionWeight?: number;
    
    viseme?: VisemeType;
    
    visemeWeight?: number;
}

export interface FacialAnimationClip {
    
    name: string;
    
    duration: number;
    
    keyframes: FacialAnimationKeyframe[];
    
    loop: boolean;
    
    userData?: any;
}

export interface AnimationGenerationRequest {
    
    id: string;
    
    prompt: string;
    
    type: 'body' | 'facial' | 'combined';
    
    duration: number;
    
    loop: boolean;
    
    referenceClip?: AnimationClip | FacialAnimationClip;
    
    style?: string;
    
    intensity?: number;
    
    seed?: number;
    
    userData?: any;
}

export interface AnimationGenerationResult {
    
    id: string;
    
    success: boolean;
    
    error?: string;
    
    clip?: AnimationClip | FacialAnimationClip;
    
    generationTime?: number;
    
    userData?: any;
}

export interface IAIAnimationModel {
    
    initialize(): Promise<boolean>;
    
    generateBodyAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    
    generateFacialAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    
    generateCombinedAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult>;
    
    analyzeEmotion(text: string): Promise<EmotionAnalysisResult>;
    
    cancelRequest(id: string): boolean;
    
    dispose(): void;
}

export interface LipSyncAIPredictorConfig {
    
    debug?: boolean;
    
    modelPath?: string;
    
    useLocalModel?: boolean;
    
    useOnlineLearning?: boolean;
    
    batchSize?: number;
    
    contextWindowSize?: number;
    
    confidenceThreshold?: number;
    
    useMFCC?: boolean;
    
    useSpectrogram?: boolean;
    
    useContext?: boolean;
}

export interface PredictionResult {
    
    viseme: VisemeType;
    
    confidence: number;
    
    alternatives?: Map<VisemeType, number>;
}

export interface LocalAIAnimationModelConfig {
    
    debug?: boolean;
    
    modelPath?: string;
    
    vocabPath?: string;
    
    batchSize?: number;
}

export interface AIAnimationSynthesisConfig {
    
    debug?: boolean;
    
    modelUrl?: string;
    
    useLocalModel?: boolean;
    
    batchSize?: number;
    
    sampleRate?: number;
    
    maxContextLength?: number;
}

export interface AdvancedAnalyzerConfig {
    
    debug?: boolean;
    
    fftSize?: number;
    
    sampleRate?: number;
    
    volumeThreshold?: number;
    
    smoothingFactor?: number;
    
    useMFCC?: boolean;
    
    useLPC?: boolean;
    
    useContextPrediction?: boolean;
    
    contextWindowSize?: number;
    
    numFrequencyBands?: number;
    
    numMelFilters?: number;
    
    numCepstralCoeffs?: number;
    
    useAIPrediction?: boolean;
    
    useSpectrogram?: boolean;
    
    usePhonemeRecognition?: boolean;
    
    useSpeechPatternRecognition?: boolean;
    
    useOnlineLearning?: boolean;
}

export interface BlendTreeNode {
    
    clipName: string;
    
    threshold: number | {
        x: number;
        y: number;
    };
    
    weight: number;
}

export interface BlendTreeParams {
    
    paramName: string;
    
    paramValue: number;
    
    nodes: BlendTreeNode[];
    
    blendType: '1D' | '2D' | 'Direct';
}

export interface WeightedBlendParams {
    
    weights: {
        [clipName: string]: number;
    };
    
    normalize: boolean;
}

export interface LayeredBlendParams {
    
    layers: {
        
        clipName: string;
        
        layerIndex: number;
        
        weight: number;
        
        mask?: string[];
    }[];
}

export interface SequentialBlendParams {
    
    sequence: {
        
        clipName: string;
        
        duration: number;
        
        transitionTime: number;
    }[];
    
    currentIndex: number;
    
    loop: boolean;
}

export interface BlendLayer {
    
    clipName: string;
    
    weight: number;
    
    timeScale: number;
    
    blendMode: BlendMode;
    
    mask?: string[];
    
    crossFadeTime?: number;
    
    blendTreeParams?: BlendTreeParams;
    
    weightParams?: WeightedBlendParams;
    
    layerParams?: LayeredBlendParams;
    
    sequenceParams?: SequentialBlendParams;
}

export interface AnimationClipOptions {
    
    name?: string;
    
    tracks?: AnimationTrack[];
    
    duration?: number;
    
    loopMode?: LoopMode;
    
    speed?: number;
    
    enabled?: boolean;
    
    weight?: number;
    
    blendTime?: number;
}

export interface AnimationEventData {
    
    type: AnimationEventType;
    
    name: string;
    
    time: number;
    
    clipName: string;
    
    params?: any;
}

export interface AnimationInstanceConfig {
    
    clip: AnimationClip | THREE.AnimationClip;
    
    timeOffset?: number;
    
    timeScale?: number;
    
    weight?: number;
    
    loop?: boolean;
}

export interface AnimationInstanceGroup {
    
    id: string;
    
    clip: AnimationClip | THREE.AnimationClip;
    
    instances: AnimationInstanceComponent[];
    
    instancedMesh?: THREE.InstancedMesh;
    
    instancedSkeleton?: THREE.InstancedBufferAttribute;
    
    boneCount: number;
    
    needsUpdate: boolean;
}

export interface AnimationInstancingSystemConfig {
    
    useGPUSkinning?: boolean;
    
    useInstancedMesh?: boolean;
    
    maxInstances?: number;
    
    debug?: boolean;
}

export interface AnimationMaskConfig {
    
    name?: string;
    
    type?: MaskType;
    
    weightType?: MaskWeightType;
    
    bones?: string[];
    
    boneWeights?: Map<string, number>;
    
    debug?: boolean;
    
    boneHierarchy?: Map<string, string[]>;
    
    rootBone?: string;
    
    distanceWeightConfig?: {
        
        maxDistance: number;
        
        minWeight: number;
        
        falloffFunction?: 'linear' | 'quadratic' | 'exponential' | 'custom';
        
        customFalloff?: (distance: number, maxDistance: number) => number;
    };
    
    gradientWeightConfig?: {
        
        startWeight: number;
        
        endWeight: number;
        
        gradientFunction?: 'linear' | 'smooth' | 'custom';
        
        customGradient?: (level: number, maxLevel: number) => number;
    };
    
    dynamicWeightConfig?: {
        
        updateFunction: (bone: string, time: number, clip: THREE.AnimationClip) => number;
        
        updateFrequency?: number;
    };
}

export interface EnhancedMaskConfig extends AnimationMaskConfig {
    
    dynamicType?: DynamicMaskType;
    
    dynamicParams?: {
        
        target?: THREE.Vector3;
        
        maxDistance?: number;
        
        minDistance?: number;
        
        direction?: THREE.Vector3;
        
        maxAngle?: number;
        
        velocityThreshold?: number;
        
        timeCurve?: (time: number) => number;
        
        paramName?: string;
        
        paramRange?: [number, number];
    };
    
    enableHierarchyCache?: boolean;
    
    enableWeightInterpolation?: boolean;
    
    weightInterpolationSpeed?: number;
}

export interface OptimizerConfig {
    
    level?: OptimizationLevel;
    
    enableCache?: boolean;
    
    enableObjectPool?: boolean;
    
    enableBatchProcessing?: boolean;
    
    enableMultiThreading?: boolean;
    
    enableGPUAcceleration?: boolean;
    
    enableLOD?: boolean;
    
    debug?: boolean;
}

export interface BoneMapping {
    
    source: string;
    
    target: string;
    
    rotationOffset?: THREE.Quaternion;
    
    positionScale?: number;
    
    mirror?: boolean;
}

export interface RetargetConfig {
    
    boneMapping: BoneMapping[];
    
    preservePositionTracks?: boolean;
    
    preserveScaleTracks?: boolean;
    
    normalizeRotations?: boolean;
    
    adjustRootHeight?: boolean;
    
    adjustBoneLength?: boolean;
    
    useQuaternionSlerp?: boolean;
    
    autoCreateMapping?: boolean;
    
    ignoreUnmappedBones?: boolean;
}

export interface RetargetingConfig {
    
    boneMapping: BoneMapping[];
    
    preservePositionTracks?: boolean;
    
    preserveScaleTracks?: boolean;
    
    normalizeRotations?: boolean;
    
    adjustRootHeight?: boolean;
    
    adjustBoneLength?: boolean;
}

export interface AnimationState {
    
    name: string;
    
    type: string;
    
    [key: string]: any;
}

export interface SingleAnimationState extends AnimationState {
    
    type: 'SingleAnimationState';
    
    clipName: string;
    
    loop: boolean;
    
    clamp: boolean;
}

export interface BlendAnimationState extends AnimationState {
    
    type: 'BlendAnimationState';
    
    parameterName: string;
    
    blendSpaceType: '1D' | '2D';
    
    blendSpaceConfig: any;
}

export interface TransitionRule {
    
    from: string;
    
    to: string;
    
    condition: () => boolean;
    
    conditionExpression?: string;
    
    duration: number;
    
    canInterrupt: boolean;
    
    curveType?: string;
    
    priority?: number;
}

export interface ParameterMetadata {
    
    minValue?: number;
    
    maxValue?: number;
    
    enumValues?: string[];
    
    description?: string;
}

export interface DebugEvent {
    
    type: DebugEventType;
    
    time: number;
    
    data: any;
}

export interface AnimationStateData {
    
    name: string;
    
    type: string;
    
    position?: {
        x: number;
        y: number;
    };
    
    color?: string;
    
    [key: string]: any;
}

export interface TransitionRuleData {
    
    from: string;
    
    to: string;
    
    conditionExpression: string;
    
    duration: number;
    
    canInterrupt: boolean;
    
    curveType?: string;
    
    priority?: number;
}

export interface ParameterData {
    
    name: string;
    
    type: 'number' | 'boolean' | 'string' | 'vector2' | 'vector3';
    
    defaultValue: any;
    
    minValue?: number;
    
    maxValue?: number;
}

export interface AnimationStateMachineData {
    
    states: AnimationStateData[];
    
    transitions: TransitionRuleData[];
    
    parameters: ParameterData[];
    
    currentState?: string;
}

export interface SubClipConfig {
    
    name?: string;
    
    originalClipName?: string;
    
    startTime?: number;
    
    endTime?: number;
    
    loop?: boolean;
    
    reverse?: boolean;
    
    timeScale?: number;
    
    debug?: boolean;
}

export interface AnimationSystemConfig {
    
    enabled?: boolean;
    
    debug?: boolean;
    
    updateFrequency?: number;
    
    useCache?: boolean;
    
    cacheSize?: number;
    
    useObjectPool?: boolean;
    
    objectPoolSize?: number;
    
    useBatchProcessing?: boolean;
    
    batchSize?: number;
    
    useGPUAcceleration?: boolean;
    
    useWorker?: boolean;
}

export interface AnimatorOptions {
    
    entity?: Entity;
    
    clips?: AnimationClip[];
    
    autoPlay?: boolean;
    
    defaultBlendTime?: number;
    
    timeScale?: number;
}

export interface BlendSpaceNode {
    
    clip: AnimationClip;
    
    position: number;
    
    weight: number;
    
    userData?: any;
}

export interface BlendSpace1DConfig {
    
    minValue: number;
    
    maxValue: number;
    
    normalizeInput?: boolean;
    
    useSmoothing?: boolean;
    
    smoothingFactor?: number;
    
    enableExtrapolation?: boolean;
}

export interface BlendSpace2DConfig {
    
    minX: number;
    
    maxX: number;
    
    minY: number;
    
    maxY: number;
    
    normalizeInput?: boolean;
    
    useTriangulation?: boolean;
}

export interface FacialAnimationConfig {
    
    debug?: boolean;
    
    autoDetectAudio?: boolean;
    
    useWebcam?: boolean;
    
    useAIPrediction?: boolean;
    
    blendSpeed?: number;
    
    smoothingFactor?: number;
}

export interface FacialAnimationEditorConfig {
    
    debug?: boolean;
    
    defaultFrameRate?: number;
    
    defaultDuration?: number;
    
    defaultBlendTime?: number;
}

export interface FacialExpressionConfig {
    
    type: FacialExpressionType;
    
    intensity: number;
    
    regions: FacialRegion[];
    
    duration?: number;
    
    loop?: boolean;
    
    blendWeight?: number;
}

export interface GPUFacialAnimationConfig {
    
    useComputeShader?: boolean;
    
    debug?: boolean;
    
    maxBlendShapes?: number;
    
    textureSize?: number;
}

export interface EnhancedGPUFacialAnimationConfig {
    
    debug?: boolean;
    
    useComputeShader?: boolean;
    
    maxBlendShapes?: number;
    
    textureSize?: number;
    
    useWebGPU?: boolean;
    
    useWebGL2?: boolean;
    
    useWebGL1Fallback?: boolean;
    
    useMobileOptimization?: boolean;
    
    useInstancing?: boolean;
    
    useAutoLOD?: boolean;
    
    lodDistanceThresholds?: number[];
    
    useAsyncCompute?: boolean;
    
    useSharedMemory?: boolean;
}

export interface GPUSkinningConfig {
    
    maxBones?: number;
    
    useComputeShader?: boolean;
    
    debug?: boolean;
}

export interface GPUSkinningSystemOptions {
    
    useGPUSkinning?: boolean;
    
    useAnimationInstancing?: boolean;
    
    useAnimationMerging?: boolean;
    
    useAnimationLOD?: boolean;
    
    useAnimationCache?: boolean;
    
    useAnimationCompression?: boolean;
    
    useDebugVisualization?: boolean;
    
    maxBones?: number;
    
    maxInstances?: number;
    
    updateInterval?: number;
}

export interface AnimationInstanceData {
    
    id: string;
    
    entity: Entity;
    
    animationComponent: AnimationComponent;
    
    skinnedMeshComponent: SkinnedMeshComponent;
    
    boneMatrices: Float32Array;
    
    animationTime: number;
    
    animationWeight: number;
    
    animationSpeed: number;
    
    loop: boolean;
    
    paused: boolean;
    
    visible: boolean;
    
    needsUpdate: boolean;
    
    userData: any;
}

export interface AnimationBatchGroup {
    
    id: string;
    
    boneCount: number;
    
    instanceCount: number;
    
    boneTexture: THREE.DataTexture | null;
    
    boneTextureSize: number;
    
    boneMatrices: Float32Array;
    
    instances: AnimationInstanceData[];
    
    instanceToIndex: Map<string, number>;
    
    availableIndices: number[];
    
    needsUpdate: boolean;
    
    visible: boolean;
    
    userData: any;
}

export interface InputAnimationIntegrationConfig {
    
    debug?: boolean;
    
    autoUpdateParameters?: boolean;
    
    moveActionName?: string;
    
    jumpActionName?: string;
    
    runActionName?: string;
    
    attackActionName?: string;
    
    defendActionName?: string;
    
    interactActionName?: string;
    
    customActionMappings?: Map<string, string>;
    
    useGestureInput?: boolean;
    
    useVoiceInput?: boolean;
}

export interface LipSyncConfig {
    
    debug?: boolean;
    
    fftSize?: number;
    
    sampleRate?: number;
    
    volumeThreshold?: number;
    
    smoothingFactor?: number;
    
    useAIPrediction?: boolean;
    
    analysisInterval?: number;
    
    useWorker?: boolean;
    
    useGPU?: boolean;
    
    numFrequencyBands?: number;
    
    useAdvancedAnalyzer?: boolean;
    
    useMFCC?: boolean;
    
    useLPC?: boolean;
    
    useContextPrediction?: boolean;
    
    contextWindowSize?: number;
    
    numMelFilters?: number;
    
    numCepstralCoeffs?: number;
    
    useSpectrogram?: boolean;
    
    usePhonemeRecognition?: boolean;
    
    useSpeechPatternRecognition?: boolean;
    
    useOnlineLearning?: boolean;
    
    aiModelPath?: string;
}

export interface PhysicsObjectConfig {
    
    type: PhysicsObjectType;
    
    mass?: number;
    
    position?: THREE.Vector3;
    
    quaternion?: THREE.Quaternion;
    
    shape?: CANNON.Shape;
    
    material?: CANNON.Material;
    
    isKinematic?: boolean;
    
    linearDamping?: number;
    
    angularDamping?: number;
    
    collisionGroup?: number;
    
    collisionMask?: number;
    
    userData?: any;
}

export interface PhysicsConstraintConfig {
    
    type: PhysicsConstraintType;
    
    bodyA: CANNON.Body;
    
    bodyB: CANNON.Body;
    
    pivotA?: CANNON.Vec3;
    
    pivotB?: CANNON.Vec3;
    
    axisA?: CANNON.Vec3;
    
    axisB?: CANNON.Vec3;
    
    maxForce?: number;
    
    collideConnected?: boolean;
    
    userData?: any;
}

export interface PhysicsEngineConfig {
    
    gravity?: CANNON.Vec3;
    
    defaultMaterial?: CANNON.Material;
    
    allowSleep?: boolean;
    
    iterations?: number;
    
    quatNormalizeFast?: boolean;
    
    quatNormalizeSkip?: number;
    
    debug?: boolean;
}

export interface SoftBodyConfig {
    
    type: SoftBodyType;
    
    name: string;
    
    position: THREE.Vector3;
    
    size: THREE.Vector3;
    
    mass?: number;
    
    stiffness?: number;
    
    damping?: number;
    
    pressure?: number;
    
    resolution?: THREE.Vector2;
    
    fixedEdges?: boolean;
    
    useGravity?: boolean;
    
    tearable?: boolean;
    
    tearThreshold?: number;
}

export interface EnhancedFacialMuscleSimulationSystemConfig {
    
    debug?: boolean;
    
    gravity?: THREE.Vector3;
    
    iterations?: number;
    
    useSoftBodies?: boolean;
    
    useGPU?: boolean;
    
    useAdaptiveTimeStep?: boolean;
    
    minTimeStep?: number;
    
    maxTimeStep?: number;
    
    useCollisionDetection?: boolean;
    
    useContinuousCollisionDetection?: boolean;
    
    useSleepState?: boolean;
    
    useConstraintSolver?: boolean;
    
    useParallelComputing?: boolean;
    
    usePhysicsMaterials?: boolean;
    
    usePhysicsDebugRenderer?: boolean;
}

export interface MuscleConfig {
    
    type: MuscleType;
    
    name: string;
    
    start: THREE.Vector3;
    
    end: THREE.Vector3;
    
    mass?: number;
    
    radius?: number;
    
    stiffness?: number;
    
    damping?: number;
    
    maxForce?: number;
    
    fixedStart?: boolean;
    
    fixedEnd?: boolean;
}

export interface FacialMuscleSimulationConfig {
    
    debug?: boolean;
    
    physicsEngine?: CannonPhysicsEngine;
    
    gravity?: THREE.Vector3;
    
    iterations?: number;
    
    useSoftBodies?: boolean;
}

export interface FacialMuscleSimulationSystemConfig {
    
    debug?: boolean;
    
    gravity?: THREE.Vector3;
    
    iterations?: number;
    
    useSoftBodies?: boolean;
}

export interface PhysicsAnimationIntegrationConfig {
    
    debug?: boolean;
    
    autoUpdateParameters?: boolean;
    
    useCharacterController?: boolean;
    
    usePhysicsDrivenBones?: boolean;
    
    physicsDrivenBones?: string[];
    
    physicsBonesDamping?: number;
    
    physicsBonesRestitution?: number;
    
    physicsBonesMass?: number;
}

export interface PhysicsBasedAnimationConfig {
    
    debug?: boolean;
    
    physicsUpdateRate?: number;
    
    useSubsteps?: boolean;
    
    substeps?: number;
    
    useCCD?: boolean;
    
    gravity?: THREE.Vector3;
    
    damping?: number;
    
    restitution?: number;
    
    friction?: number;
}

export interface PhysicsBoneConfig {
    
    name: string;
    
    mass: number;
    
    radius: number;
    
    length: number;
    
    damping?: number;
    
    restitution?: number;
    
    friction?: number;
    
    isKinematic?: boolean;
    
    collisionGroup?: number;
    
    collisionMask?: number;
}

export interface RetargetingSystemConfig {
    
    debug?: boolean;
    
    autoCreateMapping?: boolean;
    
    cacheResults?: boolean;
    
    maxCacheSize?: number;
}

export interface RetargetingResult {
    
    sourceClipName: string;
    
    targetClipName: string;
    
    sourceEntityId: string;
    
    targetEntityId: string;
    
    clip: THREE.AnimationClip;
    
    config: RetargetingConfig;
    
    timestamp: number;
}

export interface SkeletonAnimationOptions {
    
    entity?: Entity;
    
    skinnedMesh?: THREE.SkinnedMesh;
    
    clips?: AnimationClip[];
    
    autoPlay?: boolean;
    
    defaultBlendTime?: number;
    
    timeScale?: number;
}

export interface SubClipEditorConfig {
    
    name?: string;
    
    debug?: boolean;
}

export interface SubClipEventConfig {
    
    name?: string;
    
    triggerType?: EventTriggerType;
    
    triggerValue?: number;
    
    triggerCondition?: (time: number, progress: number, clip: SubClip | AnimationSubClip) => boolean;
    
    callback?: (event: any) => void;
    
    userData?: any;
    
    once?: boolean;
    
    enabled?: boolean;
    
    debug?: boolean;
}

export interface SubClipModifierConfig {
    
    name?: string;
    
    type?: ModifierType;
    
    params?: any;
    
    enabled?: boolean;
    
    debug?: boolean;
}

export interface SubClipSequenceItem {
    
    subClip: SubClip | AnimationSubClip;
    
    duration: number;
    
    transitionTime: number;
    
    weight: number;
    
    userData?: any;
}

export interface SubClipSequenceConfig {
    
    name?: string;
    
    loop?: boolean;
    
    autoPlay?: boolean;
    
    debug?: boolean;
}

export interface SubClipTransitionConfig {
    
    name?: string;
    
    fromClip?: SubClip | AnimationSubClip;
    
    toClip?: SubClip | AnimationSubClip;
    
    duration?: number;
    
    type?: TransitionType;
    
    customTransition?: (t: number) => number;
    
    debug?: boolean;
}

export interface MaskRule {
    
    path: string;
    
    weight: number;
    
    recursive?: boolean;
}

export interface BlendPerformanceData {
    
    operation: string;
    
    startTime: number;
    
    endTime: number;
    
    duration: number;
    
    layerCount?: number;
    
    maskCount?: number;
    
    blendMode?: string;
    
    blendFactor?: number;
    
    isComplete?: boolean;
    
    useCache?: boolean;
    
    useObjectPool?: boolean;
    
    useBatchProcessing?: boolean;
    
    metadata?: Record<string, any>;
}

export interface AssetInfo {
    
    id: string;
    
    name: string;
    
    type: AssetType;
    
    url: string;
    
    data?: any;
    
    metadata?: Record<string, any>;
    
    loaded: boolean;
    
    error?: Error;
}

export interface AssetManagerOptions {
    
    baseUrl?: string;
    
    resourceOptions?: any;
    
    enableDependencyManagement?: boolean;
}

export interface LoaderOptions {
    
    basePath?: string;
    
    crossOrigin?: string;
    
    dracoDecoderPath?: string;
    
    ktx2TranscoderPath?: string;
    
    basisTranscoderPath?: string;
    
    enableDraco?: boolean;
    
    enableKTX2?: boolean;
    
    enableBasis?: boolean;
    
    maxTextureSize?: number;
    
    enableTextureCompression?: boolean;
    
    enableHDR?: boolean;
}

export interface DependencyInfo {
    
    id: string;
    
    type: DependencyType;
    
    priority?: number;
    
    metadata?: Record<string, any>;
}

export interface EnhancedResourceDependencyManagerOptions {
    
    enableCycleDetection?: boolean;
    
    enableOptimization?: boolean;
    
    enableAutoOptimization?: boolean;
    
    enableDeepAnalysis?: boolean;
    
    enableDependencyCache?: boolean;
    
    debug?: boolean;
}

export interface ResourceInfo {
    
    id: string;
    
    type: AssetType;
    
    url: string;
    
    data: any;
    
    state: ResourceState;
    
    refCount: number;
    
    lastAccessTime: number;
    
    size: number;
    
    error?: Error;
    
    metadata?: Record<string, any>;
    
    priority?: number;
    
    group?: string;
    
    tags?: string[];
}

export interface EnhancedResourceManagerOptions {
    
    maxCacheSize?: number;
    
    cleanupThreshold?: number;
    
    cleanupInterval?: number;
    
    autoCleanup?: boolean;
    
    enablePreload?: boolean;
    
    maxConcurrentLoads?: number;
    
    retryCount?: number;
    
    retryDelay?: number;
    
    loadTimeout?: number;
    
    enableCompression?: boolean;
    
    enableVersioning?: boolean;
    
    versionQueryParam?: string;
    
    resourceVersion?: string;
    
    loaderOptions?: LoaderOptions;
    
    debug?: boolean;
}

export interface PreloadResourceInfo {
    
    id: string;
    
    type: AssetType;
    
    url: string;
    
    priority?: number;
    
    group?: string;
    
    metadata?: Record<string, any>;
    
    tags?: string[];
}

export interface PreloadGroupInfo {
    
    name: string;
    
    priority: number;
    
    dependencies?: string[];
    
    resources: PreloadResourceInfo[];
    
    metadata?: Record<string, any>;
    
    tags?: string[];
}

export interface PreloadProgressInfo {
    
    group: string;
    
    loaded: number;
    
    total: number;
    
    progress: number;
    
    loadedResources: string[];
    
    failedResources: string[];
    
    currentResource?: string;
    
    elapsedTime?: number;
    
    estimatedTimeRemaining?: number;
    
    loadingSpeed?: number;
}

export interface EnhancedResourcePreloaderOptions {
    
    resourceManager: EnhancedResourceManager;
    
    dependencyManager?: EnhancedResourceDependencyManager;
    
    autoRegisterAssets?: boolean;
    
    autoLoadDependencies?: boolean;
    
    maxConcurrentLoads?: number;
    
    retryCount?: number;
    
    retryDelay?: number;
    
    enableProgressEstimation?: boolean;
    
    debug?: boolean;
}

export interface EnhancedResourceSystemOptions {
    
    resourceManagerOptions?: EnhancedResourceManagerOptions;
    
    dependencyManagerOptions?: EnhancedResourceDependencyManagerOptions;
    
    preloaderOptions?: Omit<EnhancedResourcePreloaderOptions, 'resourceManager' | 'dependencyManager'>;
    
    debug?: boolean;
}

export interface ResourceVersion {
    
    id: string;
    
    resourceId: string;
    
    version: number;
    
    timestamp: number;
    
    url: string;
    
    type: AssetType;
    
    hash: string;
    
    size: number;
    
    metadata?: Record<string, any>;
    
    description: string;
    
    userId: string;
    
    userName: string;
    
    tags?: string[];
}

export interface VersionComparisonResult {
    
    version1: ResourceVersion;
    
    version2: ResourceVersion;
    
    hasDifferences: boolean;
    
    differenceType: 'content' | 'metadata' | 'both' | 'none';
    
    contentDifferences?: any;
    
    metadataDifferences?: {
        added: Record<string, any>;
        removed: Record<string, any>;
        modified: Record<string, {
            from: any;
            to: any;
        }>;
    };
    
    comparisonTimestamp: number;
}

export interface EnhancedResourceVersionManagerOptions {
    
    enabled?: boolean;
    
    maxVersionsPerResource?: number;
    
    autoCreateInitialVersion?: boolean;
    
    saveFullCopy?: boolean;
    
    enableComparison?: boolean;
    
    enableTags?: boolean;
    
    debug?: boolean;
}

export interface ResourceManagerOptions {
    
    maxCacheSize?: number;
    
    cleanupThreshold?: number;
    
    cleanupInterval?: number;
    
    autoCleanup?: boolean;
    
    enablePreload?: boolean;
    
    maxConcurrentLoads?: number;
}

export interface ResourcePreloaderOptions {
    
    assetManager: AssetManager;
    
    autoRegisterAssets?: boolean;
    
    autoLoadDependencies?: boolean;
    
    maxConcurrentLoads?: number;
    
    retryCount?: number;
    
    retryDelay?: number;
}

export interface AudioListenerOptions {
    
    position?: THREE.Vector3;
    
    forward?: THREE.Vector3;
    
    up?: THREE.Vector3;
    
    velocity?: THREE.Vector3;
}

export interface AudioSourceOptions {
    
    id: string;
    
    type?: AudioType;
    
    context: AudioContext;
    
    listener: AudioListener;
    
    destination?: AudioNode;
    
    buffer?: AudioBuffer;
    
    loop?: boolean;
    
    volume?: number;
    
    muted?: boolean;
    
    playbackRate?: number;
    
    spatial?: boolean;
    
    position?: THREE.Vector3;
    
    velocity?: THREE.Vector3;
    
    orientation?: THREE.Vector3;
    
    refDistance?: number;
    
    maxDistance?: number;
    
    rolloffFactor?: number;
    
    coneInnerAngle?: number;
    
    coneOuterAngle?: number;
    
    coneOuterGain?: number;
    
    detune?: number;
}

export interface AudioSystemOptions {
    
    enabled?: boolean;
    
    volume?: number;
    
    muted?: boolean;
    
    typeVolumes?: {
        [key in AudioType]?: number;
    };
    
    typeMuted?: {
        [key in AudioType]?: boolean;
    };
    
    spatialAudio?: boolean;
    
    autoLoad?: boolean;
    
    maxSimultaneous?: number;
    
    contextOptions?: AudioContextOptions;
}

export interface AIFacialAnimationAdapterConfig {
    
    debug?: boolean;
    
    useLocalModel?: boolean;
    
    modelPath?: string;
    
    useGPU?: boolean;
    
    enableAdvancedFeatures?: boolean;
}

export interface AIFacialAnimationRequest {
    
    id: string;
    
    prompt: string;
    
    duration: number;
    
    loop: boolean;
    
    style?: string;
    
    intensity?: number;
    
    modelType: string;
    
    useAdvancedFeatures: boolean;
}

export interface AIFacialAnimationResult {
    
    id: string;
    
    success: boolean;
    
    error?: string;
    
    clipId?: string;
}

export interface EmotionAnalysisRequest {
    
    text: string;
    
    includeSecondary?: boolean;
    
    includeChanges?: boolean;
    
    detail?: 'low' | 'medium' | 'high';
    
    emotionCategories?: string[];
    
    useContext?: boolean;
    
    contextText?: string;
    
    language?: string;
    
    customOptions?: Record<string, any>;
}

export interface EmotionTransition {
    
    from: string;
    
    to: string;
    
    probability: number;
}

export interface EmotionTimePoint {
    
    time: number;
    
    emotion: string;
    
    intensity: number;
}

export interface EmotionChangeResult {
    
    transitions: EmotionTransition[];
    
    timeline: EmotionTimePoint[];
}

export interface DetailedEmotionResult {
    
    primary: {
        
        category: string;
        
        subcategories: Record<string, string[]>;
    };
    
    intensity: {
        
        overall: number;
        
        arousal: number;
        
        valence: number;
    };
    
    confidence: number;
}

export interface AdvancedEmotionAnalyzerConfig {
    
    modelType?: AIModelType;
    
    modelVariant?: string;
    
    useLocalModel?: boolean;
    
    modelPath?: string;
    
    useGPU?: boolean;
    
    useQuantized?: boolean;
    
    quantizationBits?: 8 | 16 | 32;
    
    batchSize?: number;
    
    emotionCategories?: string[];
    
    useCache?: boolean;
    
    cacheSize?: number;
    
    debug?: boolean;
    
    useBatchProcessing?: boolean;
    
    maxWaitTime?: number;
    
    useContext?: boolean;
    
    contextWindowSize?: number;
}

export interface EmotionChangePoint {
    
    time: number;
    
    emotion: string;
    
    intensity: number;
}

export interface AdvancedEmotionBasedAnimationGeneratorConfig {
    
    debug?: boolean;
    
    useLocalModel?: boolean;
    
    modelPath?: string;
    
    useGPU?: boolean;
    
    keyframeDensity?: number;
    
    enableExpressionBlending?: boolean;
    
    enableMicroExpressions?: boolean;
    
    enableEmotionTransitions?: boolean;
    
    enableNaturalVariation?: boolean;
    
    modelType?: 'bert' | 'roberta' | 'distilbert' | 'albert' | 'xlnet' | 'custom';
    
    modelVariant?: string;
    
    useQuantized?: boolean;
    
    quantizationBits?: 8 | 16 | 32;
    
    batchSize?: number;
    
    emotionCategories?: string[];
    
    useCache?: boolean;
    
    cacheSize?: number;
    
    useContext?: boolean;
    
    contextWindowSize?: number;
    
    microExpressionConfig?: any;
    
    blendConfig?: any;
    
    usePhysics?: boolean;
    
    physicsParams?: Record<string, any>;
}

export interface MicroExpressionConfig {
    
    enabled: boolean;
    
    frequency: number;
    
    intensity: number;
    
    duration: number;
    
    types: string[];
    
    randomness: number;
}

export interface ExpressionBlendConfig {
    
    mode: 'add' | 'multiply' | 'override' | 'weighted';
    
    weight?: number;
    
    transitionTime: number;
    
    emotionChanges?: EmotionTimePoint[];
}

export interface ExpressionGenerationRequest {
    
    id: string;
    
    emotionResult: EmotionAnalysisResult;
    
    duration: number;
    
    loop?: boolean;
    
    style?: ExpressionStyle;
    
    intensity?: number;
    
    enableMicroExpressions?: boolean;
    
    microExpressionConfig?: MicroExpressionConfig;
    
    blendConfig?: ExpressionBlendConfig;
    
    customParams?: Record<string, any>;
}

export interface ExpressionKeyframe {
    
    time: number;
    
    blendShapes: Record<string, number>;
    
    emotion?: string;
    
    intensity?: number;
}

export interface MicroExpressionData {
    
    type: string;
    
    startTime: number;
    
    duration: number;
    
    intensity: number;
    
    blendShapes: Record<string, number>;
}

export interface ExpressionData {
    
    keyframes: ExpressionKeyframe[];
    
    duration: number;
    
    loop: boolean;
    
    name: string;
    
    type: string;
    
    intensity: number;
    
    microExpressions?: MicroExpressionData[];
}

export interface ExpressionGenerationResult {
    
    id: string;
    
    success: boolean;
    
    error?: string;
    
    expressionData?: ExpressionData;
    
    generationTime?: number;
}

export interface AdvancedExpressionGeneratorConfig {
    
    debug?: boolean;
    
    defaultStyle?: ExpressionStyle;
    
    defaultIntensity?: number;
    
    enableMicroExpressions?: boolean;
    
    microExpressionConfig?: Partial<MicroExpressionConfig>;
    
    blendConfig?: Partial<ExpressionBlendConfig>;
    
    usePhysics?: boolean;
    
    physicsParams?: Record<string, any>;
}

export interface BlendShapeMapping {
    
    emotionToBlendShapes: Record<string, Record<string, number>>;
    
    microExpressionToBlendShapes: Record<string, Record<string, number>>;
}

export interface FaceDetectionConfig {
    
    minFaceSize?: number;
    
    maxFaces?: number;
    
    confidenceThreshold?: number;
    
    detectLandmarks?: boolean;
    
    analyzeAttributes?: boolean;
    
    assessQuality?: boolean;
    
    debug?: boolean;
}

export interface Face3DReconstructionConfig {
    
    modelPath?: string;
    
    useGPU?: boolean;
    
    outputResolution?: number;
    
    generateBlendShapes?: boolean;
    
    debug?: boolean;
}

export interface TextureGenerationConfig {
    
    resolution?: number;
    
    generateNormalMap?: boolean;
    
    generateRoughnessMap?: boolean;
    
    generateAOMap?: boolean;
    
    quality?: 'low' | 'medium' | 'high' | 'ultra';
    
    enableSuperResolution?: boolean;
    
    debug?: boolean;
}

export interface AIProcessingManagerConfig {
    
    faceDetection?: FaceDetectionConfig;
    
    face3DReconstruction?: Face3DReconstructionConfig;
    
    textureGeneration?: TextureGenerationConfig;
    
    enableCache?: boolean;
    
    cacheSize?: number;
    
    debug?: boolean;
}

export interface DigitalHumanGenerationRequest {
    
    id: string;
    
    photoFile: File;
    
    options: {
        
        resolution?: number;
        
        quality?: 'low' | 'medium' | 'high' | 'ultra';
        
        generateExtraMaps?: boolean;
    };
    
    onProgress?: (progress: number, stage: string) => void;
}

export interface ProcessedPhoto {
    
    imageData: ImageData;
    
    width: number;
    
    height: number;
    
    faceBounds: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    
    landmarks: Array<{
        x: number;
        y: number;
    }>;
    
    confidence: number;
}

export interface FaceMesh {
    
    vertices: Float32Array;
    
    faces: Uint32Array;
    
    normals: Float32Array;
    
    uvs: Float32Array;
    
    colors?: Float32Array;
    
    blendShapes?: Map<string, Float32Array>;
}

export interface UVMapping {
    
    uvs: Float32Array;
    
    regions: {
        face: {
            u: number;
            v: number;
            width: number;
            height: number;
        };
        eyes: {
            u: number;
            v: number;
            width: number;
            height: number;
        };
        mouth: {
            u: number;
            v: number;
            width: number;
            height: number;
        };
        nose: {
            u: number;
            v: number;
            width: number;
            height: number;
        };
    };
}

export interface FaceTexture {
    
    diffuseMap: THREE.Texture;
    
    normalMap?: THREE.Texture;
    
    roughnessMap?: THREE.Texture;
    
    metallicMap?: THREE.Texture;
    
    aoMap?: THREE.Texture;
    
    heightMap?: THREE.Texture;
    
    resolution: number;
    
    uvMapping: UVMapping;
}

export interface DigitalHumanGenerationResult {
    
    requestId: string;
    
    processedPhoto: ProcessedPhoto;
    
    faceMesh: FaceMesh;
    
    faceTexture: FaceTexture;
    
    processingTime: number;
    
    qualityScore: number;
}

export interface BERTModelConfig {
    
    debug?: boolean;
    
    modelPath?: string;
    
    vocabPath?: string;
    
    useGPU?: boolean;
    
    batchSize?: number;
    
    useQuantized?: boolean;
    
    useCache?: boolean;
    
    cacheSize?: number;
    
    useRemoteAPI?: boolean;
    
    remoteAPIUrl?: string;
    
    apiKey?: string;
}

export interface ChineseBERTModelConfig {
    
    useRemoteAPI?: boolean;
    
    remoteAPIUrl?: string;
    
    apiKey?: string;
    
    modelPath?: string;
    
    useGPU?: boolean;
    
    useCache?: boolean;
    
    cacheSize?: number;
    
    debug?: boolean;
    
    useQuantized?: boolean;
    
    quantizationBits?: 8 | 16 | 32;
    
    batchSize?: number;
    
    modelVariant?: 'base' | 'large' | 'distilled';
    
    emotionCategories?: string[];
    
    useMultiLabel?: boolean;
    
    confidenceThreshold?: number;
    
    useChineseTokenizer?: boolean;
    
    tokenizerPath?: string;
    
    tokenizerType?: ChineseTokenizerType;
    
    useDictionaryEnhancement?: boolean;
    
    emotionDictionaryPath?: string;
    
    dialectType?: ChineseDialectType;
    
    useContextAnalysis?: boolean;
    
    contextWindowSize?: number;
}

export interface DistilBERTEmotionModelConfig {
    
    useRemoteAPI?: boolean;
    
    remoteAPIUrl?: string;
    
    apiKey?: string;
    
    modelPath?: string;
    
    useGPU?: boolean;
    
    useCache?: boolean;
    
    cacheSize?: number;
    
    debug?: boolean;
    
    useQuantized?: boolean;
    
    quantizationBits?: 8 | 16 | 32;
    
    batchSize?: number;
    
    modelVariant?: 'base' | 'multilingual';
    
    emotionCategories?: string[];
    
    confidenceThreshold?: number;
    
    maxSequenceLength?: number;
}

export interface EmotionBasedAnimationGeneratorConfig {
    
    debug?: boolean;
    
    useLocalModel?: boolean;
    
    modelPath?: string;
    
    useGPU?: boolean;
    
    modelType?: 'bert' | 'roberta' | 'distilbert' | 'albert' | 'xlnet' | 'custom';
    
    modelVariant?: string;
    
    useQuantized?: boolean;
    
    quantizationBits?: 8 | 16 | 32;
    
    batchSize?: number;
    
    emotionCategories?: string[];
    
    useCache?: boolean;
    
    cacheSize?: number;
}

export interface EmotionModelFactoryConfig {
    
    debug?: boolean;
    
    useCache?: boolean;
    
    cacheSize?: number;
    
    useGPU?: boolean;
    
    useRemoteAPI?: boolean;
    
    remoteAPIUrl?: string;
    
    apiKey?: string;
    
    modelPathPrefix?: string;
}

export interface FaceFeatures {
    
    geometry: {
        
        contour: THREE.Vector2[];
        
        eyes: {
            left: THREE.Vector2[];
            right: THREE.Vector2[];
        };
        
        nose: THREE.Vector2[];
        
        mouth: THREE.Vector2[];
        
        eyebrows: {
            left: THREE.Vector2[];
            right: THREE.Vector2[];
        };
    };
    
    texture: {
        
        skinColor: THREE.Color;
        
        eyeColor: THREE.Color;
        
        hairColor: THREE.Color;
    };
    
    shapeParams: Float32Array;
    
    expressionParams: Float32Array;
}

export interface FaceLandmark {
    
    type: LandmarkType;
    
    x: number;
    
    y: number;
    
    confidence: number;
}

export interface FaceInfo {
    
    id: string;
    
    boundingBox: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    
    confidence: number;
    
    landmarks: FaceLandmark[];
    
    angles: {
        yaw: number;
        pitch: number;
        roll: number;
    };
    
    quality: {
        overall: number;
        sharpness: number;
        brightness: number;
        contrast: number;
    };
    
    attributes: {
        age?: number;
        gender?: 'male' | 'female';
        emotion?: string;
        glasses?: boolean;
        beard?: boolean;
        mustache?: boolean;
    };
}

export interface FaceDetectionResult {
    
    detected: boolean;
    
    faceCount: number;
    
    faces: FaceInfo[];
    
    processingTime: number;
}

export interface MultilingualEmotionModelConfig {
    
    useRemoteAPI?: boolean;
    
    remoteAPIUrl?: string;
    
    apiKey?: string;
    
    modelPath?: string;
    
    useGPU?: boolean;
    
    useCache?: boolean;
    
    cacheSize?: number;
    
    debug?: boolean;
    
    useQuantized?: boolean;
    
    quantizationBits?: 8 | 16 | 32;
    
    batchSize?: number;
    
    modelVariant?: 'base' | 'large' | 'distilled';
    
    emotionCategories?: string[];
    
    useMultiLabel?: boolean;
    
    confidenceThreshold?: number;
    
    defaultLanguage?: SupportedLanguage;
    
    autoDetectLanguage?: boolean;
    
    useContextAnalysis?: boolean;
    
    contextWindowSize?: number;
    
    chineseDialectType?: ChineseDialectType;
}

export interface RoBERTaEmotionModelConfig {
    
    useRemoteAPI?: boolean;
    
    remoteAPIUrl?: string;
    
    apiKey?: string;
    
    modelPath?: string;
    
    useGPU?: boolean;
    
    useCache?: boolean;
    
    cacheSize?: number;
    
    debug?: boolean;
    
    useQuantized?: boolean;
    
    quantizationBits?: 8 | 16 | 32;
    
    batchSize?: number;
    
    modelVariant?: 'base' | 'large' | 'distilled';
    
    emotionCategories?: string[];
    
    useMultiLabel?: boolean;
    
    confidenceThreshold?: number;
}

export interface ConflictResolverConfig {
    
    autoResolve?: boolean;
    
    severityThreshold?: number;
    
    debug?: boolean;
}

export interface ActionClip {
    
    id: string;
    
    name: string;
    
    type: ActionType;
    
    animationData: any;
    
    duration: number;
    
    loop: boolean;
    
    priority: ActionPriority;
    
    tags: string[];
    
    thumbnail?: string;
}

export interface SequenceNode {
    
    id: string;
    
    name: string;
    
    clipId: string;
    
    startTime: number;
    
    duration: number;
    
    weight: number;
    
    blendMode: BlendMode;
    
    easing: EasingType;
    
    fadeInTime: number;
    
    fadeOutTime: number;
    
    muted: boolean;
    
    parameters: Map<string, any>;
}

export interface ActionTrack {
    
    id: string;
    
    name: string;
    
    type: 'body' | 'face' | 'hand' | 'custom';
    
    nodes: SequenceNode[];
    
    muted: boolean;
    
    locked: boolean;
    
    color: string;
}

export interface ActionSequence {
    
    id: string;
    
    name: string;
    
    description: string;
    
    duration: number;
    
    frameRate: number;
    
    tracks: ActionTrack[];
    
    loop: boolean;
    
    createdAt: Date;
    
    updatedAt: Date;
    
    tags: string[];
    
    metadata: Map<string, any>;
}

export interface PlaybackState {
    
    isPlaying: boolean;
    
    isPaused: boolean;
    
    currentTime: number;
    
    playbackSpeed: number;
    
    loopCount: number;
}

export interface ComposerConfig {
    
    defaultFrameRate: number;
    
    maxTracks: number;
    
    maxDuration: number;
    
    autoSave: boolean;
    
    autoSaveInterval: number;
    
    debug: boolean;
}

export interface ExpressionSequence {
    
    id: string;
    
    name: string;
    
    keyframes: ExpressionKeyframe[];
    
    duration: number;
    
    loop: boolean;
    
    blendMode: ExpressionBlendMode;
    
    tags: string[];
}

export interface ExpressionPreset {
    
    id: string;
    
    name: string;
    
    type: ExpressionPresetType;
    
    description: string;
    
    expressions: Array<{
        type: FacialExpressionType;
        weight: number;
        delay?: number;
        duration?: number;
    }>;
    
    intensity: ExpressionIntensity;
    
    culture?: string;
    
    tags: string[];
    
    thumbnail?: string;
}

export interface ActionPreset {
    
    id: string;
    
    name: string;
    
    description: string;
    
    expressionSequence: ExpressionSequence;
    
    bodyAnimation?: any;
    
    audio?: string;
    
    duration: number;
    
    tags: string[];
}

export interface RealTimeControlParams {
    
    intensity: number;
    
    blendSpeed: number;
    
    randomVariation: number;
    
    microExpressionFrequency: number;
    
    blinkFrequency: number;
    
    breathingIntensity: number;
}

export interface AdvancedExpressionSystemConfig {
    
    enableMicroExpressions: boolean;
    
    enableAutoBlink: boolean;
    
    enableBreathing: boolean;
    
    enableEmotionAnalysis: boolean;
    
    enableCulturalAdaptation: boolean;
    
    defaultBlendSpeed: number;
    
    debug: boolean;
}

export interface AnimationKeyframe {
    
    time: number;
    
    transforms: Map<StandardBoneType, {
        position: THREE.Vector3;
        rotation: THREE.Quaternion;
        scale: THREE.Vector3;
    }>;
}

export interface AnimationCondition {
    
    type: 'parameter' | 'time' | 'event' | 'custom';
    
    parameter?: string;
    
    operator: 'equals' | 'notEquals' | 'greater' | 'less' | 'greaterEqual' | 'lessEqual';
    
    value: any;
    
    customCondition?: () => boolean;
}

export interface AnimationTransition {
    
    id: string;
    
    fromState: string;
    
    toState: string;
    
    duration: number;
    
    curve: 'linear' | 'easeIn' | 'easeOut' | 'easeInOut' | 'custom';
    
    customCurve?: (t: number) => number;
    
    conditions: AnimationCondition[];
    
    canBeInterrupted: boolean;
}

export interface AnimationBlender {
    
    id: string;
    
    name: string;
    
    type: 'additive' | 'override' | 'multiply';
    
    inputStates: string[];
    
    outputWeight: number;
    
    blendParameters: Map<string, number>;
}

export interface AnimationBlendingSystemConfig {
    
    maxConcurrentAnimations?: number;
    
    defaultTransitionDuration?: number;
    
    enableCompression?: boolean;
    
    updateFrequency?: number;
    
    debug?: boolean;
}

export interface StateMachineConfig {
    
    defaultState?: string;
    
    debug?: boolean;
    
    maxTransitionTime?: number;
}

export interface TransitionOptions {
    
    fadeTime?: number;
    
    loop?: boolean;
    
    speed?: number;
}

export interface StateMachineParameter {
    
    name: string;
    
    value: number;
    
    type: 'float' | 'int' | 'bool' | 'trigger';
}

export interface BIPBoneAnimation {
    
    boneName: string;
    
    positionKeys: BIPKeyframe<THREE.Vector3>[];
    
    rotationKeys: BIPKeyframe<THREE.Quaternion>[];
    
    scaleKeys: BIPKeyframe<THREE.Vector3>[];
}

export interface BIPAnimationData {
    
    name: string;
    
    frameRate: number;
    
    frameCount: number;
    
    duration: number;
    
    boneAnimations: Map<string, BIPBoneAnimation>;
    
    metadata: {
        version?: string;
        creator?: string;
        description?: string;
        tags?: string[];
    };
}

export interface AnimationRetargetMapping {
    
    boneMapping: Map<string, StandardBoneType>;
    
    positionScale: number;
    
    rotationOffset: Map<StandardBoneType, THREE.Quaternion>;
    
    preserveRootMotion: boolean;
}

export interface BIPAnimationLoaderConfig {
    
    autoRetarget?: boolean;
    
    defaultFrameRate?: number;
    
    compressAnimation?: boolean;
    
    compressionPrecision?: number;
    
    optimizeKeyframes?: boolean;
    
    debug?: boolean;
}

export interface BIPPositionKeyframe {
    
    time: number;
    
    position: THREE.Vector3;
    
    interpolation?: 'linear' | 'bezier' | 'step';
}

export interface BIPPositionTrack {
    
    boneName: string;
    
    keyframes: BIPPositionKeyframe[];
}

export interface BIPRotationKeyframe {
    
    time: number;
    
    rotation: THREE.Quaternion;
    
    interpolation?: 'linear' | 'slerp' | 'step';
}

export interface BIPRotationTrack {
    
    boneName: string;
    
    keyframes: BIPRotationKeyframe[];
}

export interface BIPScaleKeyframe {
    
    time: number;
    
    scale: THREE.Vector3;
    
    interpolation?: 'linear' | 'step';
}

export interface BIPScaleTrack {
    
    boneName: string;
    
    keyframes: BIPScaleKeyframe[];
}

export interface DOFLimits {
    
    rotationX: {
        min: number;
        max: number;
    };
    
    rotationY: {
        min: number;
        max: number;
    };
    
    rotationZ: {
        min: number;
        max: number;
    };
    
    position?: {
        x: {
            min: number;
            max: number;
        };
        y: {
            min: number;
            max: number;
        };
        z: {
            min: number;
            max: number;
        };
    };
}

export interface BIPBone {
    
    name: string;
    
    id: number;
    
    parentId: number;
    
    position: THREE.Vector3;
    
    rotation: THREE.Quaternion;
    
    scale: THREE.Vector3;
    
    worldMatrix: THREE.Matrix4;
    
    bindMatrix: THREE.Matrix4;
    
    children: number[];
    
    length: number;
    
    type: BIPBoneType;
    
    dofLimits?: DOFLimits;
}

export interface BIPBoneNode {
    
    boneId: number;
    
    children: BIPBoneNode[];
    
    depth: number;
}

export interface BIPBoneHierarchy {
    
    root: BIPBoneNode;
    
    depthMap: Map<number, number>;
    
    maxDepth: number;
}

export interface BIPSkeletonData {
    
    version: string;
    
    bones: BIPBone[];
    
    rootBoneId: number;
    
    boneMap: Map<string, number>;
    
    hierarchy: BIPBoneHierarchy;
    
    metadata: {
        creator?: string;
        created?: Date;
        modified?: Date;
        description?: string;
        units?: string;
        frameRate?: number;
    };
}

export interface BIPAnimation {
    
    name: string;
    
    duration: number;
    
    frameRate: number;
    
    positionTracks: BIPPositionTrack[];
    
    rotationTracks: BIPRotationTrack[];
    
    scaleTracks: BIPScaleTrack[];
    
    sourceSkeleton: BIPSkeletonData;
}

export interface StandardAnimationTrack {
    
    name: string;
    
    targetBone: StandardBoneType;
    
    type: 'position' | 'rotation' | 'scale';
    
    times: Float32Array;
    
    values: Float32Array;
    
    interpolation: THREE.InterpolationModes;
}

export interface StandardAnimation {
    
    name: string;
    
    duration: number;
    
    frameRate: number;
    
    tracks: StandardAnimationTrack[];
}

export interface VisemeKeyframe extends FacialAnimationKeyframe {
    
    viseme: VisemeType;
}

export interface BlendShapeKeyframe extends FacialAnimationKeyframe {
    
    blendShapeName: string;
}

export interface FacialAnimationPresetSystemConfig {
    
    debug?: boolean;
    
    autoLoadPresets?: boolean;
    
    presetsPath?: string;
    
    useLocalStorage?: boolean;
}

export interface FacialAnimationPreset {
    
    id: string;
    
    name: string;
    
    description?: string;
    
    category?: string;
    
    tags?: string[];
    
    author?: string;
    
    version?: string;
    
    createdAt?: string;
    
    updatedAt?: string;
    
    thumbnail?: string;
    
    duration: number;
    
    loop?: boolean;
    
    keyframes: FacialAnimationKeyframe[];
    
    metadata?: any;
}

export interface FacialAnimationTemplate {
    
    id: string;
    
    name: string;
    
    description?: string;
    
    category?: string;
    
    tags?: string[];
    
    author?: string;
    
    version?: string;
    
    createdAt?: string;
    
    updatedAt?: string;
    
    thumbnail?: string;
    
    parameters: {
        
        id: string;
        
        name: string;
        
        description?: string;
        
        type: 'number' | 'boolean' | 'string' | 'enum';
        
        defaultValue: any;
        
        min?: number;
        
        max?: number;
        
        step?: number;
        
        options?: {
            value: any;
            label: string;
        }[];
    }[];
    
    generate: (parameters: any) => FacialAnimationPreset;
    
    metadata?: any;
}

export interface FusionManagerConfig {
    
    debug?: boolean;
    
    maxConcurrentProcessing?: number;
    
    autoResolveConflicts?: boolean;
}

export interface ActionConflict {
    
    type: ActionConflictType;
    
    animation1: AnimationClip;
    
    animation2: AnimationClip;
    
    conflictBones?: string[];
    
    description: string;
    
    severity: number;
}

export interface ConflictResolution {
    
    type: ConflictResolutionType;
    
    originalName?: string;
    
    newName?: string;
    
    conflictBones?: string[];
    
    remapping?: Map<string, string>;
    
    timeOffset?: number;
    
    priority?: number;
    
    reason: string;
}

export interface BIPImportResult {
    
    fileName: string;
    
    success: boolean;
    
    animationCount?: number;
    
    error?: string;
}

export interface BatchImportResult {
    
    totalFiles: number;
    
    successCount: number;
    
    results: BIPImportResult[];
    
    conflicts: ActionConflict[];
    
    actionCount: number;
}

export interface BoneHierarchy {
    
    root: string;
    
    parentChild: Map<string, string[]>;
    
    childParent: Map<string, string>;
    
    depthMap: Map<string, number>;
}

export interface UnifiedSkeleton {
    
    bones: string[];
    
    hierarchy: BoneHierarchy;
    
    mapping: Map<string, string>;
}

export interface BIPData {
    
    fileName: string;
    
    skeleton: BIPSkeletonData;
    
    animations: BIPAnimation[];
}

export interface TransitionCondition {
    
    parameter: string;
    
    comparison: ComparisonType;
    
    threshold: number;
}

export interface StateTransition {
    
    fromState: string;
    
    toState: string;
    
    duration: number;
    
    conditions: TransitionCondition[];
}

export interface PlayActionOptions {
    
    fadeTime?: number;
    
    loop?: boolean;
    
    speed?: number;
}

export interface ActionStep {
    
    actionName: string;
    
    fadeTime: number;
    
    waitTime: number;
    
    loop: boolean;
    
    speed: number;
}

export interface BlendAction {
    
    name: string;
    
    weight: number;
}

export interface ActionBlendConfig {
    
    actions: BlendAction[];
    
    blendMode: BlendMode;
    
    duration: number;
}

export interface ActionLibraryItem {
    
    name: string;
    
    duration: number;
    
    sourceFile: string;
    
    status: 'ready' | 'processing';
    
    hasConflict: boolean;
    
    clip: AnimationClip;
}

export interface BIPParserConfig {
    
    validateStructure?: boolean;
    
    calculateWorldTransforms?: boolean;
    
    generateHierarchy?: boolean;
    
    unitScale?: number;
    
    debug?: boolean;
}

export interface StandardBone {
    
    type: StandardBoneType;
    
    name: string;
    
    parentType?: StandardBoneType;
    
    position: THREE.Vector3;
    
    rotation: THREE.Quaternion;
    
    scale: THREE.Vector3;
    
    worldMatrix: THREE.Matrix4;
    
    bindMatrix: THREE.Matrix4;
    
    children: StandardBoneType[];
    
    originalBoneId?: number;
}

export interface StandardBoneNode {
    
    boneType: StandardBoneType;
    
    children: StandardBoneNode[];
    
    depth: number;
}

export interface StandardBoneHierarchy {
    
    root: StandardBoneNode;
    
    depthMap: Map<StandardBoneType, number>;
    
    maxDepth: number;
}

export interface StandardSkeletonData {
    
    bones: Map<StandardBoneType, StandardBone>;
    
    rootBoneType: StandardBoneType;
    
    hierarchy: StandardBoneHierarchy;
    
    originalBIPData: BIPSkeletonData;
}

export interface BoneMappingRule {
    
    bipNamePattern: string | RegExp;
    
    standardType: StandardBoneType;
    
    priority: number;
    
    required: boolean;
    
    transformAdjustment?: {
        positionOffset?: THREE.Vector3;
        rotationOffset?: THREE.Euler;
        scaleMultiplier?: THREE.Vector3;
    };
}

export interface MappingConfig {
    
    strictMode?: boolean;
    
    autoFixMissing?: boolean;
    
    preserveOriginalTransforms?: boolean;
    
    debug?: boolean;
}

export interface ClothingPhysics {
    
    enabled: boolean;
    
    mass: number;
    
    stiffness: number;
    
    damping: number;
    
    elasticity: number;
    
    friction: number;
    
    airResistance: number;
    
    gravityScale: number;
}

export interface ClothingFittingParams {
    
    scale: THREE.Vector3;
    
    offset: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    boneWeights?: Map<string, number>;
    
    morphTargetWeights?: Map<string, number>;
}

export interface ClothingItem {
    
    id: string;
    
    name: string;
    
    category: ClothingCategory;
    
    slotType: ClothingSlotType;
    
    geometryUrl: string;
    
    textureUrls: Map<string, string>;
    
    materialType: ClothingMaterialType;
    
    materialProperties: Record<string, any>;
    
    physics: ClothingPhysics;
    
    fittingParams: ClothingFittingParams;
    
    compatibleBodyTypes: string[];
    
    price?: number;
    
    tags: string[];
    
    creator?: string;
    
    version: string;
}

export interface FittedClothing {
    
    item: ClothingItem;
    
    geometry: THREE.BufferGeometry;
    
    material: THREE.Material;
    
    mesh: THREE.Mesh;
    
    fittingScore: number;
    
    appliedParams: ClothingFittingParams;
}

export interface ClothingOutfit {
    
    id: string;
    
    name: string;
    
    items: Map<ClothingSlotType, string>;
    
    tags: string[];
    
    createdAt: Date;
    
    updatedAt: Date;
}

export interface ClothingSystemConfig {
    
    enablePhysics: boolean;
    
    enableAutoFitting: boolean;
    
    enableCollisionDetection: boolean;
    
    fittingQualityThreshold: number;
    
    maxClothingItems: number;
    
    debug: boolean;
}

export interface AnimationGraphNode {
    
    type: AnimationGraphNodeType;
    
    name: string;
    
    data: any;
}

export interface AnimationGraph {
    
    nodes: Map<string, AnimationGraphNode>;
    
    currentNode: AnimationGraphNode | null;
    
    blending: boolean;
    
    blendStrength: number;
    
    blendTargetNode: AnimationGraphNode | null;
}

export interface AvatarComponentConfig {
    
    enabled?: boolean;
    
    type?: AvatarType;
    
    userId?: string;
    
    name?: string;
    
    modelUrl?: string;
}

export interface BoneInfo {
    
    entity: Entity;
    
    bone: THREE.Bone;
    
    initialLocalRotation: THREE.Quaternion;
    
    initialWorldRotation: THREE.Quaternion;
    
    initialWorldRotationInverse: THREE.Quaternion;
    
    parentWorldRotation: THREE.Quaternion;
    
    parentWorldRotationInverse: THREE.Quaternion;
}

export interface FaceGeometry {
    
    mesh: THREE.Mesh;
    
    vertices: Float32Array;
    
    normals: Float32Array;
    
    uvs: Float32Array;
    
    landmarks: THREE.Vector3[];
    
    boundingBox: THREE.Box3;
}

export interface BodyMorphTargets {
    
    height: number;
    
    weight: number;
    
    muscle: number;
    
    chest: number;
    
    waist: number;
    
    hips: number;
    
    shoulders: number;
    
    custom: Map<string, number>;
}

export interface ClothingSlot {
    
    type: ClothingSlotType;
    
    currentItemId?: string;
    
    itemUrl?: string;
    
    metadata?: Record<string, any>;
    
    enabled: boolean;
    
    fittingParams?: {
        scale: THREE.Vector3;
        offset: THREE.Vector3;
        rotation: THREE.Euler;
    };
}

export interface ClothingSlots {
    
    slots: Map<ClothingSlotType, ClothingSlot>;
    
    currentOutfitId?: string;
    
    outfitName?: string;
}

export interface PersonalityTraits {
    
    personalityType: string;
    
    emotionalState: string;
    
    behaviorPattern: string;
    
    voiceCharacteristics: {
        pitch: number;
        speed: number;
        tone: string;
    };
    
    movementPreferences: {
        walkingStyle: string;
        gestureFrequency: number;
        posture: string;
    };
    
    customTraits: Map<string, any>;
}

export interface DigitalHumanComponentConfig extends AvatarComponentConfig {
    
    source?: DigitalHumanSource;
    
    sourcePhotoUrl?: string;
    
    sourceFileUrl?: string;
    
    sourceData?: {
        originalFile?: string;
        importData?: any;
        [key: string]: any;
    };
    
    faceGeometry?: FaceGeometry;
    
    bodyMorphTargets?: BodyMorphTargets;
    
    clothingSlots?: ClothingSlots;
    
    personalityTraits?: PersonalityTraits;
    
    version?: string;
    
    creatorId?: string;
    
    licenseType?: string;
    
    tags?: string[];
}

export interface FacialExpression {
    expression: FacialExpressionType;
    weight: number;
}

export interface Viseme {
    viseme: VisemeType;
    weight: number;
}

export interface MuscleData {
    
    type: MuscleType;
    
    name: string;
    
    start: THREE.Vector3;
    
    end: THREE.Vector3;
    
    mass: number;
    
    radius: number;
    
    stiffness: number;
    
    damping: number;
    
    fixedStart: boolean;
    
    fixedEnd: boolean;
    
    maxStretch?: number;
    
    maxCompress?: number;
    
    restLength?: number;
    
    controlPoints?: THREE.Vector3[];
}

export interface PhysicalMuscle extends MuscleData {
    
    bodies?: any[];
    
    constraints?: any[];
    
    currentForce?: THREE.Vector3;
    
    targetPosition?: THREE.Vector3;
    
    initialPosition?: THREE.Vector3;
    
    active?: boolean;
}

export interface ExpressionMuscleConfig {
    
    muscleName: string;
    
    forceDirection: THREE.Vector3;
    
    forceMagnitude: number;
    
    targetPosition?: THREE.Vector3;
    
    stiffness?: number;
    
    damping?: number;
}

export interface ExpressionConfig {
    
    expression: FacialExpressionType;
    
    muscles: ExpressionMuscleConfig[];
}

export interface ActionEvent {
    
    name: string;
    
    time: number;
    
    params?: Record<string, any>;
}

export interface ActionData {
    
    id: string;
    
    name: string;
    
    type: ActionType;
    
    priority: ActionPriority;
    
    animationName: string;
    
    interruptible: boolean;
    
    loop: boolean;
    
    transitionTime: number;
    
    duration: number;
    
    params?: Record<string, any>;
    
    events?: ActionEvent[];
}

export interface ActionInstance {
    
    data: ActionData;
    
    entity: Entity;
    
    startTime: number;
    
    endTime: number;
    
    isPlaying: boolean;
    
    isCompleted: boolean;
    
    triggeredEvents: Set<string>;
}

export interface ActionControlSystemConfig {
    
    debug?: boolean;
    
    maxConcurrentActions?: number;
    
    enableActionBlending?: boolean;
    
    enableActionQueue?: boolean;
    
    enableActionEvents?: boolean;
    
    enablePhysicsDrivenActions?: boolean;
    
    enableActionRecording?: boolean;
    
    enableActionPlayback?: boolean;
}

export interface CharacterControllerOptions {
    
    offset?: number;
    
    maxSlopeClimbAngle?: number;
    
    minSlopeSlideAngle?: number;
    
    autoStep?: {
        
        maxHeight: number;
        
        minWidth: number;
        
        stepOverDynamic: boolean;
    };
    
    enableSnapToGround?: number | false;
}

export interface AdvancedCharacterControllerConfig {
    
    walkSpeed?: number;
    
    runSpeed?: number;
    
    crouchSpeed?: number;
    
    crawlSpeed?: number;
    
    swimSpeed?: number;
    
    climbSpeed?: number;
    
    flySpeed?: number;
    
    jumpForce?: number;
    
    gravity?: number;
    
    turnSpeed?: number;
    
    airControl?: number;
    
    usePhysics?: boolean;
    
    useStateMachine?: boolean;
    
    useBlendSpace?: boolean;
    
    useIK?: boolean;
    
    useEnvironmentAwareness?: boolean;
    
    debug?: boolean;
    
    physicsControllerOptions?: CharacterControllerOptions;
}

export interface ControllerPresetData {
    
    id: string;
    
    name: string;
    
    description: string;
    
    type: ControllerPresetType;
    
    tags: string[];
    
    config: Partial<AdvancedCharacterControllerConfig>;
    
    defaultActions?: ActionData[];
    
    thumbnail?: string;
    
    author?: string;
    
    createdAt: Date;
    
    updatedAt: Date;
}

export interface ControllerTemplateParameter {
    
    id: string;
    
    name: string;
    
    description: string;
    
    type: 'number' | 'boolean' | 'string' | 'vector' | 'color' | 'enum';
    
    defaultValue: any;
    
    min?: number;
    
    max?: number;
    
    step?: number;
    
    options?: {
        value: any;
        label: string;
    }[];
    
    required?: boolean;
    
    group?: string;
}

export interface ControllerTemplateData {
    
    id: string;
    
    name: string;
    
    description: string;
    
    type: ControllerPresetType;
    
    tags: string[];
    
    baseConfig: Partial<AdvancedCharacterControllerConfig>;
    
    parameters: ControllerTemplateParameter[];
    
    defaultActions?: ActionData[];
    
    thumbnail?: string;
    
    author?: string;
    
    createdAt: Date;
    
    updatedAt: Date;
}

export interface CharacterControllerPresetManagerConfig {
    
    debug?: boolean;
    
    presetsPath?: string;
    
    autoLoadPresets?: boolean;
    
    useLocalStorage?: boolean;
}

export interface EmotionExpressionData {
    
    expression: FacialExpressionType;
    
    targetWeight: number;
    
    currentWeight: number;
    
    transitionSpeed: number;
    
    startTime: number;
    
    duration: number;
    
    active: boolean;
    
    priority: number;
}

export interface EmotionBlendControllerConfig {
    
    debug?: boolean;
    
    defaultTransitionTime?: number;
    
    enableMicroExpressions?: boolean;
    
    microExpressionFrequency?: number;
    
    microExpressionIntensity?: number;
    
    microExpressionDuration?: number;
    
    enableNaturalVariation?: boolean;
    
    naturalVariationAmount?: number;
    
    naturalVariationFrequency?: number;
    
    blendMode?: 'add' | 'multiply' | 'override' | 'weighted';
}

export interface ConversionConfig {
    
    debug?: boolean;
    
    preserveOriginal?: boolean;
    
    optimize?: boolean;
    
    compressionLevel?: number;
}

export interface DigitalHumanMetadata {
    
    name: string;
    
    creator: string;
    
    createdAt: string;
    
    modifiedAt: string;
    
    version: string;
    
    description?: string;
    
    tags?: string[];
    
    license?: string;
    
    thumbnail?: string;
}

export interface MaterialData {
    
    name: string;
    
    type: 'standard' | 'pbr' | 'toon' | 'custom';
    
    properties: {
        
        baseColor?: [number, number, number, number];
        
        metallic?: number;
        
        roughness?: number;
        
        normalScale?: number;
        
        emissive?: [number, number, number];
        
        opacity?: number;
        
        doubleSided?: boolean;
    };
    
    textureMaps?: {
        
        diffuse?: string;
        
        normal?: string;
        
        metallic?: string;
        
        roughness?: string;
        
        ao?: string;
        
        emissive?: string;
    };
}

export interface TextureData {
    
    name: string;
    
    data: string | ArrayBuffer;
    
    format: 'jpg' | 'png' | 'webp' | 'ktx2' | 'dds';
    
    size: [number, number];
    
    generateMipmaps?: boolean;
    
    wrapS?: 'repeat' | 'clamp' | 'mirror';
    
    wrapT?: 'repeat' | 'clamp' | 'mirror';
    
    minFilter?: 'nearest' | 'linear' | 'nearestMipmap' | 'linearMipmap';
    
    magFilter?: 'nearest' | 'linear';
}

export interface SkinningData {
    
    boneIndices: number[];
    
    boneWeights: number[];
    
    bindMatrices: number[][];
}

export interface MeshData {
    
    name: string;
    
    vertexCount: number;
    
    faceCount: number;
    
    materialIndex?: number;
    
    skinning?: SkinningData;
}

export interface ModelData {
    
    format: 'gltf' | 'glb' | 'fbx' | 'obj';
    
    data: string | ArrayBuffer;
    
    materials?: MaterialData[];
    
    textures?: TextureData[];
    
    meshes?: MeshData[];
}

export interface BoneData {
    
    name: string;
    
    index: number;
    
    parentIndex: number;
    
    localMatrix: number[];
    
    worldMatrix: number[];
    
    bindMatrix: number[];
    
    children: number[];
}

export interface SkeletonData {
    
    bones: BoneData[];
    
    rootBoneIndex: number;
    
    hierarchy: BoneHierarchy;
}

export interface KeyframeData {
    
    time: number;
    
    value: number[];
    
    inTangent?: number[];
    
    outTangent?: number[];
}

export interface AnimationTrackData {
    
    boneName: string;
    
    type: 'position' | 'rotation' | 'scale';
    
    keyframes: KeyframeData[];
    
    interpolation: 'linear' | 'step' | 'cubic';
}

export interface AnimationClipData {
    
    name: string;
    
    duration: number;
    
    frameRate: number;
    
    loop: boolean;
    
    tracks: AnimationTrackData[];
}

export interface AnimationData {
    
    animations: AnimationClipData[];
    
    defaultAnimation?: string;
}

export interface BlendShapeData {
    
    name: string;
    
    index: number;
    
    defaultWeight: number;
    
    minWeight: number;
    
    maxWeight: number;
}

export interface FacialPresetData {
    
    name: string;
    
    weights: Record<string, number>;
}

export interface FacialData {
    
    blendShapes: BlendShapeData[];
    
    presets: FacialPresetData[];
}

export interface PhysicsConstraintData {
    
    type: 'distance' | 'angle' | 'collision';
    
    parameters: Record<string, number>;
}

export interface ClothingPhysicsData {
    
    mass: number;
    
    damping: number;
    
    elasticity: number;
    
    constraints: PhysicsConstraintData[];
}

export interface ClothingItemData {
    
    name: string;
    
    type: 'top' | 'bottom' | 'shoes' | 'accessory' | 'hair';
    
    slot: string;
    
    model: ModelData;
    
    physics?: ClothingPhysicsData;
}

export interface ClothingData {
    
    items: ClothingItemData[];
    
    defaultOutfit?: string[];
}

export interface DigitalHumanFile {
    
    version: string;
    
    metadata: DigitalHumanMetadata;
    
    model: ModelData;
    
    skeleton?: SkeletonData;
    
    animations?: AnimationData;
    
    facial?: FacialData;
    
    clothing?: ClothingData;
    
    extensions?: Record<string, any>;
}

export interface ConversionResult {
    
    success: boolean;
    
    data?: DigitalHumanFile;
    
    format?: SupportedFileFormat;
    
    error?: string;
    
    warnings?: string[];
}

export interface DigitalHumanPackageHeader {
    
    version: string;
    
    type: 'digital-human-package';
    
    created: string;
    
    modified: string;
    
    creator: {
        name?: string;
        id?: string;
        version?: string;
    };
    
    size: number;
    
    fileCount: number;
    
    checksum: string;
}

export interface GeometryInfo {
    
    filePath: string;
    
    format: 'gltf' | 'fbx' | 'obj' | 'ply' | 'custom';
    
    vertexCount: number;
    
    faceCount: number;
    
    boundingBox: {
        min: [number, number, number];
        max: [number, number, number];
    };
    
    hasUVs: boolean;
    
    hasNormals: boolean;
    
    hasVertexColors: boolean;
    
    blendShapeCount?: number;
    
    compression?: {
        algorithm: string;
        ratio: number;
    };
}

export interface TextureInfo {
    
    type: 'diffuse' | 'normal' | 'roughness' | 'metallic' | 'ao' | 'height' | 'emission';
    
    filePath: string;
    
    format: 'jpg' | 'png' | 'webp' | 'ktx2' | 'dds';
    
    resolution: [number, number];
    
    fileSize: number;
    
    compressed: boolean;
    
    compressionFormat?: string;
    
    uvChannel: number;
    
    wrapMode: {
        s: 'repeat' | 'clamp' | 'mirror';
        t: 'repeat' | 'clamp' | 'mirror';
    };
    
    filterMode: {
        min: 'nearest' | 'linear' | 'mipmap';
        mag: 'nearest' | 'linear';
    };
}

export interface SkeletonInfo {
    
    filePath: string;
    
    format: 'standard' | 'bip' | 'mixamo' | 'custom';
    
    boneCount: number;
    
    rootBoneType: StandardBoneType;
    
    supportedBones: StandardBoneType[];
    
    hasIKConstraints: boolean;
    
    hasDOFLimits: boolean;
    
    bindPose: {
        filePath: string;
        format: 'json' | 'binary';
    };
}

export interface AnimationInfo {
    
    id: string;
    
    name: string;
    
    filePath: string;
    
    format: 'gltf' | 'fbx' | 'bvh' | 'bip' | 'custom';
    
    duration: number;
    
    frameRate: number;
    
    frameCount: number;
    
    type: 'idle' | 'walk' | 'run' | 'gesture' | 'expression' | 'custom';
    
    loop: boolean;
    
    affectedBones: StandardBoneType[];
    
    tags: string[];
    
    preview?: {
        thumbnailPath: string;
        videoPath?: string;
    };
}

export interface ClothingInfo {
    
    id: string;
    
    name: string;
    
    slotType: ClothingSlotType;
    
    geometryPath: string;
    
    textures: TextureInfo[];
    
    fittingParams: {
        scale: [number, number, number];
        offset: [number, number, number];
        rotation: [number, number, number];
    };
    
    physics?: {
        enabled: boolean;
        mass: number;
        stiffness: number;
        damping: number;
    };
    
    collision?: {
        enabled: boolean;
        meshPath: string;
    };
    
    material: {
        type: 'standard' | 'pbr' | 'custom';
        properties: Record<string, any>;
    };
}

export interface ExpressionInfo {
    
    id: string;
    
    name: string;
    
    type: 'basic' | 'emotion' | 'viseme' | 'custom';
    
    blendShapeWeights: Record<string, number>;
    
    boneTransforms?: Record<StandardBoneType, {
        position?: [number, number, number];
        rotation?: [number, number, number, number];
        scale?: [number, number, number];
    }>;
    
    intensityRange: [number, number];
    
    previewPath?: string;
}

export interface PackageFile {
    
    path: string;
    
    size: number;
    
    type: 'geometry' | 'texture' | 'animation' | 'skeleton' | 'config' | 'other';
    
    mimeType: string;
    
    checksum: string;
    
    compressed: boolean;
    
    compressedSize?: number;
    
    lastModified: string;
}

export interface PackageManifest {
    
    files: PackageFile[];
    
    directories: string[];
    
    totalSize: number;
    
    compressionRatio?: number;
}

export interface DigitalHumanPackageContent {
    
    header: DigitalHumanPackageHeader;
    
    metadata: DigitalHumanMetadata;
    
    geometry: GeometryInfo;
    
    textures: TextureInfo[];
    
    skeleton?: SkeletonInfo;
    
    animations: AnimationInfo[];
    
    clothing: ClothingInfo[];
    
    expressions: ExpressionInfo[];
    
    manifest: PackageManifest;
}

export interface PackageValidationResult {
    
    valid: boolean;
    
    errors: string[];
    
    warnings: string[];
    
    details: {
        headerValid: boolean;
        metadataValid: boolean;
        filesValid: boolean;
        checksumValid: boolean;
        structureValid: boolean;
    };
}

export interface PackageBuildOptions {
    
    compressionLevel: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
    
    includeSource: boolean;
    
    optimizeTextures: boolean;
    
    textureQuality: 'low' | 'medium' | 'high' | 'lossless';
    
    includeAnimations: boolean;
    
    includeExpressions: boolean;
    
    includeClothing: boolean;
    
    targetPlatform: 'web' | 'mobile' | 'desktop' | 'all';
    
    customMetadata?: Record<string, any>;
}

export interface PackageParseOptions {
    
    validateIntegrity: boolean;
    
    loadAllResources: boolean;
    
    resourceFilter?: {
        geometry: boolean;
        textures: boolean;
        animations: boolean;
        clothing: boolean;
        expressions: boolean;
    };
    
    useCache: boolean;
    
    onProgress?: (progress: number, stage: string) => void;
}

export interface PackageManagerConfig {
    
    workingDirectory?: string;
    
    tempDirectory?: string;
    
    cacheDirectory?: string;
    
    maxCacheSize?: number;
    
    enableCompression?: boolean;
    
    defaultCompressionLevel?: number;
    
    debug?: boolean;
}

export interface PhotoTo3DConfig {
    
    debug?: boolean;
    
    quality?: 'low' | 'medium' | 'high' | 'ultra';
    
    generateTextures?: boolean;
    
    generateNormalMaps?: boolean;
    
    optimizeMesh?: boolean;
    
    maxProcessingTime?: number;
}

export interface FacialLandmarks {
    
    points: THREE.Vector2[];
    
    confidence: number;
    
    boundingBox: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
}

export interface FaceAnalysisResult {
    
    landmarks: FacialLandmarks;
    
    pose: {
        yaw: number;
        pitch: number;
        roll: number;
    };
    
    attributes: {
        age?: number;
        gender?: 'male' | 'female';
        emotion?: string;
        glasses?: boolean;
        beard?: boolean;
    };
    
    qualityScore: number;
}

export interface MeshGenerationResult {
    
    mesh: THREE.Mesh;
    
    vertexCount: number;
    
    faceCount: number;
    
    uvCoordinates: Float32Array;
    
    normals: Float32Array;
}

export interface TextureGenerationResult {
    
    diffuseTexture: THREE.Texture;
    
    normalTexture?: THREE.Texture;
    
    roughnessTexture?: THREE.Texture;
    
    metallicTexture?: THREE.Texture;
    
    resolution: [number, number];
}

export interface ConversionProgress {
    
    stage: string;
    
    progress: number;
    
    message: string;
    
    estimatedTimeRemaining?: number;
}

export interface InteractionResponse {
    
    id: string;
    
    type: 'animation' | 'sound' | 'effect' | 'script';
    
    data: any;
    
    delay: number;
    
    duration: number;
    
    priority: number;
}

export interface InteractionZone {
    
    id: string;
    
    name: string;
    
    type: 'sphere' | 'box' | 'plane' | 'custom';
    
    position: THREE.Vector3;
    
    size: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    enabled: boolean;
    
    interactionTypes: InteractionType[];
    
    triggerConditions: Map<string, any>;
    
    responses: InteractionResponse[];
}

export interface DragController {
    
    enabled: boolean;
    
    constraints: {
        x: boolean;
        y: boolean;
        z: boolean;
    };
    
    bounds?: {
        min: THREE.Vector3;
        max: THREE.Vector3;
    };
    
    speed: number;
    
    damping: number;
    
    snapPoints: THREE.Vector3[];
    
    snapDistance: number;
}

export interface EnvironmentAdapter {
    
    environmentType: EnvironmentType;
    
    lightingAdaptation: {
        enabled: boolean;
        autoAdjust: boolean;
        brightness: number;
        contrast: number;
    };
    
    physicsAdaptation: {
        enabled: boolean;
        gravity: THREE.Vector3;
        friction: number;
        airResistance: number;
    };
    
    audioAdaptation: {
        enabled: boolean;
        reverb: number;
        echo: number;
        volume: number;
    };
}

export interface IntelligentBehavior {
    
    id: string;
    
    type: BehaviorType;
    
    enabled: boolean;
    
    priority: number;
    
    conditions: Array<{
        type: string;
        value: any;
        operator: 'equals' | 'greater' | 'less' | 'contains';
    }>;
    
    parameters: Map<string, any>;
    
    cooldown: number;
    
    lastExecuted: number;
}

export interface SceneInteractionConfig {
    
    enableDragControl: boolean;
    
    enableEnvironmentAdaptation: boolean;
    
    enableIntelligentBehavior: boolean;
    
    enableCollisionDetection: boolean;
    
    enablePhysicsSimulation: boolean;
    
    interactionCheckFrequency: number;
    
    maxInteractionDistance: number;
    
    debug: boolean;
}

export interface MarketplaceItem {
    id: string;
    title: string;
    description: string;
    creatorId: string;
    creatorName: string;
    thumbnailUrl: string;
    previewUrls: string[];
    price: number;
    currency: 'USD' | 'CNY' | 'EUR';
    license: 'free' | 'cc0' | 'cc_by' | 'commercial';
    tags: string[];
    category: string;
    downloadCount: number;
    rating: number;
    reviewCount: number;
    fileSize: number;
    version: string;
    compatibility: string[];
    createdAt: number;
    updatedAt: number;
    status: 'pending' | 'approved' | 'rejected' | 'suspended';
    featured: boolean;
}

export interface SearchQuery {
    keyword?: string;
    category?: string;
    tags?: string[];
    priceRange?: {
        min: number;
        max: number;
    };
    license?: string[];
    rating?: number;
    sortBy?: 'newest' | 'popular' | 'rating' | 'price_low' | 'price_high';
    page?: number;
    limit?: number;
}

export interface SearchResult {
    items: MarketplaceItem[];
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
}

export interface PublishInfo {
    userId: string;
    title: string;
    description: string;
    price: number;
    currency: 'USD' | 'CNY' | 'EUR';
    license: 'free' | 'cc0' | 'cc_by' | 'commercial';
    tags: string[];
    category: string;
    previewImages?: File[];
}

export interface PublishResult {
    success: boolean;
    itemId?: string;
    errors?: string[];
    warnings?: string[];
    status: 'pending' | 'approved' | 'rejected';
}

export interface DownloadResult {
    success: boolean;
    downloadUrl?: string;
    expiresAt: number;
    fileSize: number;
    error?: string;
}

export interface UserReview {
    id: string;
    userId: string;
    userName: string;
    itemId: string;
    rating: number;
    comment: string;
    createdAt: number;
    helpful: number;
}

export interface FacialAnimationPresetConfig {
    
    name: string;
    
    type: FacialAnimationPresetType;
    
    expression?: FacialExpressionType;
    
    weight?: number;
    
    expressionCombos?: {
        expression: FacialExpressionType;
        weight: number;
    }[];
    
    animationSequence?: {
        expression: FacialExpressionType;
        weight: number;
        duration: number;
    }[];
    
    culture?: string;
    
    tags?: string[];
    
    description?: string;
    
    author?: string;
    
    createdAt?: Date;
    
    updatedAt?: Date;
    
    customData?: any;
}

export interface ActionPlaybackConfig {
    
    debug?: boolean;
    
    playbackSpeed?: number;
    
    playbackInput?: boolean;
    
    playbackTransform?: boolean;
    
    loop?: boolean;
    
    autoPlay?: boolean;
}

export interface RecordedActionEvent {
    
    actionId: string;
    
    actionData: ActionData;
    
    eventType: 'start' | 'stop';
    
    timestamp: number;
    
    params?: Record<string, any>;
}

export interface RecordedInputEvent {
    
    inputType: string;
    
    value: any;
    
    timestamp: number;
}

export interface RecordedTransformEvent {
    
    position: {
        x: number;
        y: number;
        z: number;
    };
    
    rotation: {
        x: number;
        y: number;
        z: number;
        w: number;
    };
    
    timestamp: number;
}

export interface ActionRecording {
    
    id: string;
    
    name: string;
    
    startTimestamp: number;
    
    endTimestamp: number;
    
    actionEvents: RecordedActionEvent[];
    
    inputEvents: RecordedInputEvent[];
    
    transformEvents: RecordedTransformEvent[];
    
    metadata?: Record<string, any>;
}

export interface ActionRecorderConfig {
    
    debug?: boolean;
    
    recordInput?: boolean;
    
    recordTransform?: boolean;
    
    transformRecordFrequency?: number;
    
    autoStart?: boolean;
}

export interface AIAnimationSynthesisSystemConfig {
    
    debug?: boolean;
    
    useLocalModel?: boolean;
    
    modelPath?: string;
    
    useWorker?: boolean;
    
    useGPU?: boolean;
    
    modelType?: EmotionModelType;
    
    modelVariant?: EmotionModelVariant;
    
    useAdvancedGenerator?: boolean;
    
    useChineseModel?: boolean;
    
    useMultilingualModel?: boolean;
}

export interface AvatarAnimationSystemConfig {
    
    debug?: boolean;
    
    autoRetarget?: boolean;
    
    useStateMachine?: boolean;
    
    useBlendSpace?: boolean;
}

export interface BIPIntegrationConfig {
    
    debug?: boolean;
    
    autoFixMissing?: boolean;
    
    strictMode?: boolean;
    
    maxConcurrentProcessing?: number;
}

export interface ValidationResult {
    
    isValid: boolean;
    
    errors: string[];
    
    warnings: string[];
}

export interface ChineseLipSyncSystemConfig {
    
    debug?: boolean;
    
    usePinyinAnalysis?: boolean;
    
    useToneAnalysis?: boolean;
    
    useSpeechRecognition?: boolean;
    
    speechRecognitionLang?: string;
    
    usePhonemeAnalysis?: boolean;
    
    useSmoothTransition?: boolean;
    
    transitionTime?: number;
    
    contextWindowSize?: number;
    
    useContextPrediction?: boolean;
}

export interface ImportConfig {
    
    debug?: boolean;
    
    maxFileSize?: number;
    
    autoFix?: boolean;
    
    strictValidation?: boolean;
    
    supportedFormats?: SupportedFileFormat[];
}

export interface FileValidationResult {
    
    isValid: boolean;
    
    format?: SupportedFileFormat;
    
    fileSize: number;
    
    errors: string[];
    
    warnings: string[];
}

export interface ImportResult {
    
    success: boolean;
    
    digitalHuman?: Entity;
    
    assetIds?: string[];
    
    error?: string;
    
    warnings?: string[];
}

export interface ImportProgress {
    
    stage: string;
    
    progress: number;
    
    message: string;
}

export interface DigitalHumanSystemConfig {
    
    debug?: boolean;
    
    maxConcurrentGenerations?: number;
    
    autoSaveInterval?: number;
    
    enableAutoOptimization?: boolean;
}

export interface EmotionResponseSystemConfig {
    
    debug?: boolean;
    
    autoRespondToEvents?: boolean;
    
    responseRange?: number;
    
    minResponseIntensity?: number;
    
    maxResponseIntensity?: number;
    
    emotionDecayRate?: number;
    
    enableEmotionBlending?: boolean;
    
    enableEmotionMemory?: boolean;
    
    emotionMemoryDuration?: number;
    
    enableEmotionContagion?: boolean;
    
    emotionContagionRange?: number;
    
    emotionContagionStrength?: number;
}

export interface EmotionEventData {
    
    type: EmotionEventType;
    
    source?: Entity;
    
    position?: Vector3;
    
    intensity?: number;
    
    range?: number;
    
    duration?: number;
    
    description?: string;
    
    customData?: any;
}

export interface EmotionResponseData {
    
    entity: Entity;
    
    eventType: EmotionEventType;
    
    intensity: number;
    
    duration: number;
    
    expression: FacialExpressionType;
    
    description: string;
    
    timestamp: number;
}

export interface EmotionMemory {
    
    eventType: EmotionEventType;
    
    intensity: number;
    
    timestamp: number;
    
    source?: Entity;
    
    description?: string;
}

export interface EnhancedLipSyncSystemConfig {
    
    debug?: boolean;
    
    fftSize?: number;
    
    volumeThreshold?: number;
    
    analysisInterval?: number;
    
    useWorker?: boolean;
    
    numFrequencyBands?: number;
    
    useAdvancedAnalyzer?: boolean;
    
    useAIPrediction?: boolean;
    
    useGPU?: boolean;
    
    useSpeechRecognition?: boolean;
    
    speechRecognitionLang?: string;
    
    usePhonemeAnalysis?: boolean;
    
    useContextPrediction?: boolean;
    
    contextWindowSize?: number;
    
    useSmoothTransition?: boolean;
    
    transitionTime?: number;
    
    useChinesePhonemeMapping?: boolean;
}

export interface FacialAnimationEditorSystemConfig {
    
    debug?: boolean;
    
    autoSaveInterval?: number;
    
    enableUndoRedo?: boolean;
    
    maxUndoSteps?: number;
}

export interface FacialAnimationSystemConfig {
    
    debug?: boolean;
    
    autoDetectAudio?: boolean;
    
    useWebcam?: boolean;
    
    updateFrequency?: number;
}

export interface LipSyncSystemConfig {
    
    debug?: boolean;
    
    fftSize?: number;
    
    volumeThreshold?: number;
    
    analysisInterval?: number;
    
    useWorker?: boolean;
    
    numFrequencyBands?: number;
    
    useAdvancedAnalyzer?: boolean;
    
    useAIPrediction?: boolean;
    
    useGPU?: boolean;
}

export interface PhysicalFacialAnimationSystemConfig {
    
    debug?: boolean;
    
    physicsUpdateRate?: number;
    
    gravity?: THREE.Vector3;
    
    damping?: number;
    
    useGPU?: boolean;
    
    showDebugInfo?: boolean;
    
    useAdvancedPhysics?: boolean;
    
    useSoftBodyPhysics?: boolean;
    
    useCollisionDetection?: boolean;
}

export interface TestEnvironmentConfig {
    name: string;
    baseUrl: string;
    apiKey: string;
    database: {
        host: string;
        port: number;
        database: string;
        username: string;
        password: string;
    };
    storage: {
        endpoint: string;
        accessKey: string;
        secretKey: string;
    };
    timeout: {
        short: number;
        medium: number;
        long: number;
    };
}

export interface TestDataConfig {
    samplePhotos: string[];
    sampleBipFiles: string[];
    sampleDigitalHumans: string[];
    testUsers: {
        id: string;
        username: string;
        email: string;
        role: 'admin' | 'user' | 'creator';
    }[];
    performanceThresholds: {
        maxCreationTime: number;
        maxRenderTime: number;
        maxMemoryUsage: number;
        minFPS: number;
        maxLoadTime: number;
    };
}

export interface TestSuiteConfig {
    functional: {
        enabled: boolean;
        parallel: boolean;
        retryCount: number;
        timeout: number;
    };
    performance: {
        enabled: boolean;
        iterations: number;
        warmupIterations: number;
        timeout: number;
    };
    integration: {
        enabled: boolean;
        setupTimeout: number;
        teardownTimeout: number;
    };
    compatibility: {
        enabled: boolean;
        browsers: string[];
        devices: string[];
        platforms: string[];
    };
    acceptance: {
        enabled: boolean;
        userScenarios: string[];
        timeout: number;
    };
}

export interface TestReportConfig {
    outputDir: string;
    formats: ('json' | 'html' | 'xml' | 'csv')[];
    includeScreenshots: boolean;
    includeLogs: boolean;
    includeMetrics: boolean;
    emailNotification: {
        enabled: boolean;
        recipients: string[];
        onFailureOnly: boolean;
    };
}

export interface IntegrationConfig {
    enablePerformanceMonitoring: boolean;
    enableAutoOptimization: boolean;
    enableCloudSync: boolean;
    maxConcurrentDigitalHumans: number;
    performanceThresholds: {
        maxMemoryUsage: number;
        minFPS: number;
        maxCPUUsage: number;
    };
    storageConfig: {
        endpoint: string;
        accessKey: string;
        secretKey: string;
        bucket: string;
    };
    aiConfig: {
        apiKey: string;
        endpoint: string;
        model: string;
    };
}

export interface PhotoCreationData {
    photo: File | ImageData;
    userId: string;
    name?: string;
    options?: {
        quality: 'low' | 'medium' | 'high';
        generateBody: boolean;
        autoRigging: boolean;
    };
}

export interface FileCreationData {
    file: File;
    userId: string;
    name?: string;
    overrideCompatibility?: boolean;
}

export interface BIPCreationData {
    bipFile: File;
    additionalBipFiles?: File[];
    targetDigitalHuman: any;
    userId: string;
    name?: string;
    options?: {
        autoMapping: boolean;
        resolveConflicts: boolean;
        generateMissingBones: boolean;
    };
}

export interface DigitalHumanCreationData {
    type: DigitalHumanCreationType;
    userId: string;
    name?: string;
    photoData?: PhotoCreationData;
    fileData?: FileCreationData;
    marketplaceId?: string;
    bipData?: BIPCreationData;
}

export interface CompatibilityResult {
    hasErrors: boolean;
    errors: string[];
    warnings: string[];
    missingFeatures?: string[];
    versionMismatch?: {
        required: string;
        current: string;
    };
}

export interface BoneRemapping {
    sourceBone: string;
    targetBone: string;
    confidence: number;
}

export interface PerformanceImpact {
    memoryDelta: number;
    renderingImpact: number;
    cpuImpact?: number;
    networkImpact?: number;
}

export interface HealthIssue {
    component: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    suggestion?: string;
    timestamp: number;
}

export interface SystemHealthCheck {
    overall: 'healthy' | 'warning' | 'error';
    components: {
        storage: 'healthy' | 'warning' | 'error';
        ai: 'healthy' | 'warning' | 'error';
        bip: 'healthy' | 'warning' | 'error';
        marketplace: 'healthy' | 'warning' | 'error';
        versioning: 'healthy' | 'warning' | 'error';
    };
    issues: HealthIssue[];
}

export interface OptimizationSuggestion {
    type: 'memory' | 'performance' | 'quality' | 'network';
    priority: 'low' | 'medium' | 'high';
    description: string;
    action: string;
    estimatedImpact: {
        performance?: number;
        memory?: number;
        quality?: number;
    };
}

export interface IntegrationResult {
    success: boolean;
    digitalHumanId?: string;
    warnings?: string[];
    errors?: string[];
    performanceImpact?: {
        memoryDelta: number;
        renderingImpact: number;
    };
}

export interface ExportOptions {
    format: 'dhp' | 'gltf' | 'fbx' | 'obj';
    includeAnimations: boolean;
    includeTextures: boolean;
    compressionLevel: 'none' | 'low' | 'medium' | 'high';
    optimizeForWeb: boolean;
    targetPlatform?: 'web' | 'mobile' | 'desktop' | 'vr';
}

export interface ExportResult {
    success: boolean;
    filePath?: string;
    downloadUrl?: string;
    fileSize: number;
    format: string;
    warnings?: string[];
    errors?: string[];
    metadata: {
        exportTime: number;
        originalSize: number;
        compressedSize: number;
        compressionRatio: number;
    };
}

export interface CloudSyncStatus {
    isEnabled: boolean;
    lastSyncTime?: number;
    syncInProgress: boolean;
    pendingUploads: number;
    pendingDownloads: number;
    errors: string[];
    totalSynced: number;
    totalSize: number;
}

export interface SystemStatistics {
    totalDigitalHumans: number;
    activeDigitalHumans: number;
    totalMemoryUsage: number;
    averageRenderTime: number;
    totalStorageUsed: number;
    networkTraffic: {
        uploaded: number;
        downloaded: number;
    };
    userActivity: {
        activeUsers: number;
        totalSessions: number;
        averageSessionDuration: number;
    };
    systemUptime: number;
}

export interface AutoOptimizationConfig {
    enabled: boolean;
    triggers: {
        memoryThreshold: number;
        fpsThreshold: number;
        cpuThreshold: number;
    };
    actions: {
        enableLOD: boolean;
        reduceTextureQuality: boolean;
        limitConcurrentAnimations: boolean;
        enableBatching: boolean;
        cleanupInactive: boolean;
    };
    schedule: {
        interval: number;
        maintenanceWindow?: {
            start: string;
            end: string;
        };
    };
}

export interface QualitySettings {
    rendering: {
        resolution: 'low' | 'medium' | 'high' | 'ultra';
        antialiasing: boolean;
        shadows: 'off' | 'low' | 'medium' | 'high';
        reflections: boolean;
        postProcessing: boolean;
    };
    textures: {
        maxSize: number;
        compression: 'none' | 'low' | 'medium' | 'high';
        mipmaps: boolean;
    };
    animations: {
        maxFPS: number;
        interpolation: 'linear' | 'cubic';
        compression: boolean;
    };
    physics: {
        enabled: boolean;
        precision: 'low' | 'medium' | 'high';
        maxObjects: number;
    };
}

export interface SystemEventData {
    type: SystemEventType;
    timestamp: number;
    data: any;
    source: string;
    severity?: 'info' | 'warning' | 'error';
}

export interface VersionInfo {
    
    id: string;
    
    version: string;
    
    type: VersionType;
    
    status: VersionStatus;
    
    parentId?: string;
    
    branchName?: string;
    
    description: string;
    
    author: string;
    
    createdAt: Date;
    
    tags: string[];
    
    metadata: Map<string, any>;
}

export interface ChangeRecord {
    
    id: string;
    
    type: ChangeType;
    
    target: string;
    
    propertyPath?: string;
    
    oldValue?: any;
    
    newValue?: any;
    
    timestamp: Date;
    
    description: string;
}

export interface VersionSnapshot {
    
    id: string;
    
    versionId: string;
    
    digitalHumanData: any;
    
    changes: ChangeRecord[];
    
    createdAt: Date;
    
    dataSize: number;
    
    compressedSize?: number;
}

export interface BranchInfo {
    
    id: string;
    
    name: string;
    
    baseVersionId: string;
    
    currentVersionId: string;
    
    description: string;
    
    author: string;
    
    createdAt: Date;
    
    isActive: boolean;
    
    tags: string[];
}

export interface MergeConflict {
    
    id: string;
    
    propertyPath: string;
    
    sourceValue: any;
    
    targetValue: any;
    
    conflictType: 'value' | 'structure' | 'deletion';
    
    resolution?: 'source' | 'target' | 'custom';
    
    customValue?: any;
}

export interface VersionManagerConfig {
    
    maxVersions: number;
    
    maxSnapshots: number;
    
    autoSnapshot: boolean;
    
    autoSnapshotInterval: number;
    
    enableCompression: boolean;
    
    debug: boolean;
}

export interface EngineOptions {
    
    canvas?: HTMLCanvasElement | string;
    
    autoStart?: boolean;
    
    debug?: boolean;
    
    language?: string;
}

export interface LogEntry {
    timestamp: number;
    level: LogLevel;
    category: string;
    message: string;
    data?: any;
}

export interface SceneAnalysisResult {
    
    sceneId: string;
    
    sceneName: string;
    
    entityCount: number;
    
    renderableCount: number;
    
    triangleCount: number;
    
    vertexCount: number;
    
    materialCount: number;
    
    textureCount: number;
    
    textureMemory: number;
    
    lightCount: number;
    
    drawCalls: number;
    
    memoryUsage: number;
    
    suggestions: OptimizationSuggestion[];
    
    overallScore: number;
    
    timestamp: number;
}

export interface OptimizationHistoryEntry {
    
    id: string;
    
    sceneId: string;
    
    sceneName: string;
    
    analysisResult: SceneAnalysisResult;
    
    appliedOptimizations: OptimizationType[];
    
    scoreBefore: number;
    
    scoreAfter: number;
    
    timestamp: number;
    
    description: string;
}

export interface OptimizationComparisonResult {
    
    baseEntry: OptimizationHistoryEntry;
    
    compareEntry: OptimizationHistoryEntry;
    
    scoreDifference: number;
    
    scorePercentChange: number;
    
    statsChanges: {
        
        field: string;
        
        displayName: string;
        
        baseValue: number;
        
        compareValue: number;
        
        difference: number;
        
        percentChange: number;
    }[];
    
    appliedOptimizations: OptimizationType[];
}

export interface SceneOptimizerConfig {
    
    debug?: boolean;
    
    enableAutoLOD?: boolean;
    
    enableAutoInstancing?: boolean;
    
    enableAutoBatching?: boolean;
    
    enableAutoMaterialOptimization?: boolean;
    
    enableAutoTextureOptimization?: boolean;
    
    enableAutoGeometryOptimization?: boolean;
    
    enableAutoLightOptimization?: boolean;
    
    enableAutoShadowOptimization?: boolean;
    
    enableAutoMemoryOptimization?: boolean;
    
    enableOcclusionCulling?: boolean;
    
    enableDetailTexture?: boolean;
    
    enableMeshSimplification?: boolean;
    
    enableParticleOptimization?: boolean;
    
    enableAnimationOptimization?: boolean;
    
    thresholds?: {
        
        triangles?: {
            
            low: number;
            
            medium: number;
            
            high: number;
        };
        
        drawCalls?: {
            
            low: number;
            
            medium: number;
            
            high: number;
        };
        
        memory?: {
            
            low: number;
            
            medium: number;
            
            high: number;
        };
        
        particles?: {
            
            low: number;
            
            medium: number;
            
            high: number;
        };
        
        animations?: {
            
            low: number;
            
            medium: number;
            
            high: number;
        };
        
        textureResolution?: {
            
            low: number;
            
            medium: number;
            
            high: number;
        };
    };
}

export interface VolumetricFogParams {
    
    density: number;
    
    color: THREE.Color;
    
    height: number;
    
    range: number;
    
    scattering: number;
    
    absorption: number;
    
    phase: number;
    
    noiseScale: number;
    
    noiseSpeed: number;
    
    noiseStrength: number;
    
    enableOptimization: boolean;
    
    enableAdaptiveQuality: boolean;
    
    maxSteps: number;
}

export interface VolumetricFogVolume {
    
    position: THREE.Vector3;
    
    size: THREE.Vector3;
    
    density: number;
    
    color: THREE.Color;
}

export interface EnvironmentAwarenessData {
    environmentType: EnvironmentType;
    weatherType: WeatherType;
    terrainType: TerrainType;
    lightIntensity: number;
    temperature: number;
    humidity: number;
    windSpeed: number;
    windDirection: Vector3;
    noiseLevel: number;
    airQuality: number;
    waterLevel: number;
    visibility: number;
    timeOfDay: number;
    customParameters: Map<string, any>;
    lastEnvironmentChangeTime: number;
    awarenessRange: number;
}

export interface EnvironmentAwarenessComponentConfig {
    awarenessRange?: number;
    updateFrequency?: number;
    debug?: boolean;
    autoDetect?: boolean;
    changeThreshold?: number;
}

export interface EnvironmentCondition {
    type: string;
    params: any;
    evaluate: (data: EnvironmentAwarenessData) => boolean;
}

export interface EnvironmentAction {
    type: string;
    params: any;
    execute: (entity: Entity) => void;
    stop?: (entity: Entity) => void;
}

export interface EnvironmentResponseRule {
    id: string;
    name: string;
    description?: string;
    responseType: ResponseType;
    priority: ResponsePriority;
    conditions: EnvironmentCondition[];
    actions: EnvironmentAction[];
    cooldown?: number;
    lastTriggeredTime?: number;
    enabled: boolean;
}

export interface EnvironmentResponseComponentConfig {
    autoRespond?: boolean;
    debug?: boolean;
    rules?: EnvironmentResponseRule[];
}

export interface EnvironmentAwarenessSystemConfig {
    debug?: boolean;
    updateFrequency?: number;
    autoDetect?: boolean;
    detectionRange?: number;
    enableResponse?: boolean;
    usePhysicsRaycasting?: boolean;
    enableVisualization?: boolean;
}

export interface EnvironmentZone {
    id: string;
    name: string;
    environmentData: Partial<EnvironmentAwarenessData>;
    containsPoint: (point: Vector3) => boolean;
}

export interface GLTF {
    scene: THREE.Group;
    scenes: THREE.Group[];
    animations: THREE.AnimationClip[];
    cameras: THREE.Camera[];
    asset: any;
    parser: any;
    userData: any;
}

export interface GLTFExportOptions {
    
    animations?: THREE.AnimationClip[];
    
    binary?: boolean;
    
    includeCustomExtensions?: boolean;
    
    onlyVisible?: boolean;
    
    truncateDrawRange?: boolean;
    
    forceIndices?: boolean;
    
    maxTextureSize?: number;
    
    exportTransforms?: boolean;
}

export interface GLTFLoaderOptions {
    
    useDraco?: boolean;
    
    dracoDecoderPath?: string;
    
    useKTX2?: boolean;
    
    ktx2DecoderPath?: string;
    
    loadAnimations?: boolean;
    
    loadCameras?: boolean;
    
    loadLights?: boolean;
    
    optimizeGeometry?: boolean;
}

export interface GLTFSystemOptions {
    
    autoPlayAnimations?: boolean;
    
    useDraco?: boolean;
    
    dracoDecoderPath?: string;
    
    useKTX2?: boolean;
    
    ktx2DecoderPath?: string;
    
    optimizeGeometry?: boolean;
}

export interface I18nOptions {
    
    language?: string;
    
    fallbackLanguage?: string;
    
    resources?: Record<string, Record<string, string>>;
}

export interface InputAction {
    
    getName(): string;
    
    getType(): InputActionType;
    
    update(value: any): void;
    
    hasChanged(): boolean;
    
    getValue(): any;
    
    reset(): void;
    
    addEventListener(event: string, callback: (data: any) => void): void;
    
    removeEventListener(event: string, callback: (data: any) => void): void;
}

export interface InputDevice {
    
    getName(): string;
    
    initialize(): void;
    
    update(deltaTime: number): void;
    
    destroy(): void;
    
    getValue(key: string): any;
    
    setValue(key: string, value: any): void;
    
    hasKey(key: string): boolean;
    
    getKeys(): string[];
    
    on(event: string, callback: EventCallback): void;
    
    off(event: string, callback: EventCallback): void;
}

export interface InputMapping {
    
    getName(): string;
    
    getType(): InputMappingType;
    
    getDeviceName(): string;
    
    evaluate(device: InputDevice): any;
}

export interface InputComponentOptions {
    
    enabled?: boolean;
    
    actions?: InputAction[];
    
    bindings?: InputBinding[];
    
    mappings?: InputMapping[];
}

export interface GestureEventData {
    
    type: GestureType;
    
    state: GestureState;
    
    direction: GestureDirection;
    
    x: number;
    
    y: number;
    
    scale?: number;
    
    rotation?: number;
    
    velocity?: number;
    
    duration?: number;
    
    originalEvent?: Event;
}

export interface GestureRecognizerOptions {
    
    element?: HTMLElement;
    
    preventDefault?: boolean;
    
    stopPropagation?: boolean;
    
    longPressThreshold?: number;
    
    doubleTapThreshold?: number;
    
    swipeThreshold?: number;
    
    swipeVelocityThreshold?: number;
}

export interface VoiceRecognitionResult {
    
    text: string;
    
    confidence: number;
    
    isFinal: boolean;
    
    alternatives?: Array<{
        
        text: string;
        
        confidence: number;
    }>;
}

export interface VoiceCommand {
    
    name: string;
    
    keywords: string[];
    
    callback: (result: VoiceRecognitionResult) => void;
    
    confidenceThreshold?: number;
}

export interface VoiceDeviceOptions {
    
    autoStart?: boolean;
    
    language?: string;
    
    continuous?: boolean;
    
    interimResults?: boolean;
    
    maxAlternatives?: number;
    
    confidenceThreshold?: number;
    
    commands?: VoiceCommand[];
}

export interface IInputBinding {
    
    getName(): string;
    
    getMappingName(): string;
}

export interface InputManagerOptions {
    
    element?: HTMLElement;
    
    preventDefault?: boolean;
    
    stopPropagation?: boolean;
    
    enableKeyboard?: boolean;
    
    enableMouse?: boolean;
    
    enableTouch?: boolean;
    
    enableGamepad?: boolean;
    
    enableXR?: boolean;
    
    enableGesture?: boolean;
    
    enableVoice?: boolean;
    
    gestureOptions?: any;
    
    voiceOptions?: any;
}

export interface InputEvent {
    
    timestamp: number;
    
    deviceName: string;
    
    key: string;
    
    value: any;
}

export interface InputRecording {
    
    startTimestamp: number;
    
    endTimestamp: number;
    
    events: InputEvent[];
}

export interface InputRecorderOptions {
    
    autoStart?: boolean;
    
    recordAllDevices?: boolean;
    
    deviceNames?: string[];
}

export interface InputSystemOptions {
    
    element?: HTMLElement;
    
    preventDefault?: boolean;
    
    stopPropagation?: boolean;
    
    enableKeyboard?: boolean;
    
    enableMouse?: boolean;
    
    enableTouch?: boolean;
    
    enableGamepad?: boolean;
    
    enablePointerLock?: boolean;
}

export interface InputVisualizerOptions {
    
    container?: HTMLElement;
    
    showDevices?: boolean;
    
    showActions?: boolean;
    
    showEventLog?: boolean;
    
    maxEventLogEntries?: number;
    
    autoUpdate?: boolean;
    
    updateInterval?: number;
}

export interface InputVisualizerEnhancedOptions {
    
    container?: HTMLElement;
    
    showDevices?: boolean;
    
    showActions?: boolean;
    
    showEventLog?: boolean;
    
    maxEventLogEntries?: number;
    
    autoUpdate?: boolean;
    
    updateInterval?: number;
    
    showPerformance?: boolean;
    
    showCharts?: boolean;
    
    showHeatmap?: boolean;
    
    showDebugTools?: boolean;
    
    darkTheme?: boolean;
    
    draggable?: boolean;
    
    resizable?: boolean;
    
    collapsible?: boolean;
    
    initiallyCollapsed?: boolean;
    
    showDeviceDetails?: boolean;
    
    showInputHistory?: boolean;
    
    inputHistoryLength?: number;
}

export interface PerformanceData {
    
    timestamp: number;
    
    fps: number;
    
    inputLatency: number;
    
    processingTime: number;
}

export interface PerformanceMonitorOptions {
    
    sampleInterval?: number;
    
    historyLength?: number;
    
    autoAdjust?: boolean;
    
    targetFPS?: number;
    
    maxInputLatency?: number;
    
    deviceCapabilities?: DeviceCapabilities;
}

export interface ThrottleOptions {
    
    interval: number;
    
    leading?: boolean;
    
    trailing?: boolean;
}

export interface DebounceOptions {
    
    wait: number;
    
    leading?: boolean;
    
    trailing?: boolean;
    
    maxWait?: number;
}

export interface GestureGrabComponentConfig {
    
    enabled?: boolean;
    
    grabGestureType?: GestureType;
    
    releaseGestureType?: GestureType;
    
    rotateGestureType?: GestureType;
    
    scaleGestureType?: GestureType;
    
    grabDistance?: number;
    
    useRaycasting?: boolean;
    
    useHandTracking?: boolean;
    
    useGesturePrediction?: boolean;
    
    gestureSensitivity?: number;
    
    onGrab?: (entity: Entity, hand: Hand) => void;
    
    onRelease?: (entity: Entity, hand: Hand) => void;
}

export interface GrabbableComponentConfig {
    
    grabType?: GrabType;
    
    allowedHands?: Hand[];
    
    grabbable?: boolean;
    
    grabDistance?: number;
    
    grabSound?: string;
    
    releaseSound?: string;
    
    onGrab?: (entity: Entity, grabber: Entity) => void;
    
    onRelease?: (entity: Entity, grabber: Entity) => void;
}

export interface GrabbedComponentConfig {
    
    grabber: Entity;
    
    hand: Hand;
    
    offset?: {
        x: number;
        y: number;
        z: number;
    };
}

export interface GrabberComponentConfig {
    
    maxGrabDistance?: number;
    
    enabled?: boolean;
    
    onGrab?: (grabber: Entity, grabbed: Entity, hand: Hand) => void;
    
    onRelease?: (grabber: Entity, released: Entity, hand: Hand) => void;
}

export interface GrabNetworkEventData {
    
    type: GrabNetworkEventType;
    
    grabberEntityId: string;
    
    grabbedEntityId: string;
    
    hand: Hand;
    
    timestamp: number;
    
    userId: string;
    
    sessionId: string;
    
    state?: any;
    
    velocity?: {
        x: number;
        y: number;
        z: number;
    };
    
    angularVelocity?: {
        x: number;
        y: number;
        z: number;
    };
    
    rotation?: {
        x: number;
        y: number;
        z: number;
        w: number;
    };
}

export interface GrabNetworkComponentConfig {
    
    enabled?: boolean;
    
    syncInterval?: number;
    
    networkLatency?: number;
    
    useCompression?: boolean;
    
    usePrediction?: boolean;
    
    useAuthorityControl?: boolean;
    
    useAdaptiveSync?: boolean;
}

export interface InteractableComponentConfig {
    
    interactionType?: InteractionType;
    
    visible?: boolean;
    
    interactive?: boolean;
    
    interactionDistance?: number;
    
    label?: string;
    
    prompt?: string;
    
    interactionSound?: string;
    
    highlightColor?: string;
    
    onInteract?: InteractionCallback;
}

export interface InteractionEventData {
    
    type: InteractionEventType;
    
    target: Entity;
    
    source?: Entity;
    
    timestamp: number;
    
    [key: string]: any;
}

export interface InteractionEventComponentConfig {
    
    enabled?: boolean;
}

export interface InteractionHighlightComponentConfig {
    
    highlightType?: HighlightType;
    
    highlightColor?: Color | string;
    
    highlightIntensity?: number;
    
    outlineWidth?: number;
    
    pulse?: boolean;
    
    pulseSpeed?: number;
    
    enabled?: boolean;
    
    highlighted?: boolean;
}

export interface InteractionPromptComponentConfig {
    
    text?: string;
    
    icon?: string;
    
    positionType?: PromptPositionType;
    
    offset?: Vector3;
    
    duration?: number;
    
    fadeInTime?: number;
    
    fadeOutTime?: number;
    
    backgroundColor?: string;
    
    textColor?: string;
    
    borderColor?: string;
    
    borderWidth?: number;
    
    borderRadius?: number;
    
    fontSize?: number;
    
    fontFamily?: string;
    
    padding?: number;
    
    autoHide?: boolean;
    
    visible?: boolean;
}

export interface PhysicsGrabComponentConfig {
    
    grabForce?: number;
    
    grabDamping?: number;
    
    keepOriginalBodyType?: boolean;
    
    grabBodyType?: BodyType;
}

export interface ThrowableComponentConfig {
    
    throwable?: boolean;
    
    throwType?: ThrowType;
    
    throwForceMultiplier?: number;
    
    throwAngularForceMultiplier?: number;
    
    throwVelocitySmoothingFactor?: number;
    
    throwHistoryLength?: number;
    
    preserveRotationOnThrow?: boolean;
    
    throwSound?: string;
    
    onThrow?: (entity: Entity, velocity: Vector3, angularVelocity: Vector3) => void;
}

export interface XRGrabComponentConfig {
    
    enabled?: boolean;
    
    controllerType?: XRControllerType;
    
    grabButtonIndex?: number;
    
    releaseButtonIndex?: number;
    
    grabDistance?: number;
    
    useRaycasting?: boolean;
    
    useHapticFeedback?: boolean;
    
    hapticFeedbackIntensity?: number;
    
    hapticFeedbackDuration?: number;
    
    useHandPoseDetection?: boolean;
    
    onGrab?: (entity: Entity, hand: Hand) => void;
    
    onRelease?: (entity: Entity, hand: Hand) => void;
}

export interface InteractionSystemConfig {
    
    debug?: boolean;
    
    maxInteractionDistance?: number;
    
    enableFrustumCheck?: boolean;
    
    enableHighlight?: boolean;
    
    enablePrompt?: boolean;
    
    enableSound?: boolean;
}

export interface GrabEventData {
    
    grabber: Entity;
    
    grabbed: Entity;
    
    hand: Hand;
    
    timestamp: number;
    
    [key: string]: any;
}

export interface GrabSystemConfig {
    
    debug?: boolean;
    
    enablePhysicsGrab?: boolean;
    
    enableNetworkSync?: boolean;
    
    enableGestureGrab?: boolean;
    
    defaultGrabType?: GrabType;
}

export interface OptimizedGrabSystemConfig {
    
    debug?: boolean;
    
    enablePhysicsGrab?: boolean;
    
    enableNetworkSync?: boolean;
    
    enableGestureGrab?: boolean;
    
    enableXRGrab?: boolean;
    
    defaultGrabType?: GrabType;
    
    enableSpatialPartitioning?: boolean;
    
    spatialGridSize?: number;
    
    enableObjectPool?: boolean;
    
    enableMultithreading?: boolean;
    
    enableLOD?: boolean;
    
    updateFrequency?: number;
}

export interface ModelLoaderOptions {
    
    enableCache?: boolean;
    
    enableTextureCompression?: boolean;
    
    enableGeometryOptimization?: boolean;
    
    enableMaterialOptimization?: boolean;
    
    enableLODGeneration?: boolean;
    
    enableDebug?: boolean;
}

export interface MotionCaptureComponentConfig {
    
    enabled?: boolean;
    
    smoothingFactor?: number;
    
    visibilityThreshold?: number;
}

export interface MotionCapturePoseState {
    
    begun: boolean;
    
    duration?: number;
    
    intensity?: number;
    
    confidence?: number;
}

export interface MotionCapturePoseComponentConfig {
    
    enabled?: boolean;
    
    poseHoldThreshold?: number;
    
    poseAngleThreshold?: number;
}

export interface MotionCaptureSystemConfig {
    
    debug?: boolean;
    
    enableNetworkSync?: boolean;
    
    enablePoseEvaluation?: boolean;
    
    smoothingFactor?: number;
    
    visibilityThreshold?: number;
}

export interface WorldLandmarkData {
    
    x: number;
    
    y: number;
    
    z: number;
    
    visibility?: number;
}

export interface LandmarkData {
    
    x: number;
    
    y: number;
    
    z: number;
    
    visibility?: number;
}

export interface MotionCaptureResults {
    
    worldLandmarks: WorldLandmarkData[];
    
    landmarks: LandmarkData[];
}

export interface LandmarkConnection {
    
    start: number;
    
    end: number;
}

export interface LandmarkConnections {
    
    connections: LandmarkConnection[];
}

export interface BandwidthUsageData {
    
    upload: number;
    
    download: number;
    
    uploadLimit: number;
    
    downloadLimit: number;
    
    uploadUsageRatio: number;
    
    downloadUsageRatio: number;
    
    priorityUsage: Map<DataPriority, number>;
    
    timestamp: number;
}

export interface BandwidthAllocationConfig {
    
    mode: BandwidthAllocationMode;
    
    priorityAllocation?: {
        [key in DataPriority]?: number;
    };
    
    minimumGuaranteed?: {
        [key in DataPriority]?: number;
    };
    
    allowBorrowing?: boolean;
    
    enableDynamicAdjustment?: boolean;
}

export interface AdvancedBandwidthControllerConfig {
    
    maxUploadBandwidth?: number;
    
    maxDownloadBandwidth?: number;
    
    strategy?: BandwidthControlStrategy;
    
    allocation?: BandwidthAllocationConfig;
    
    targetUsage?: number;
    
    autoAdjust?: boolean;
    
    adjustInterval?: number;
    
    enableBurstControl?: boolean;
    
    burstMultiplier?: number;
    
    burstDuration?: number;
    
    enablePredictiveAllocation?: boolean;
    
    predictiveWindowSize?: number;
    
    enableSmoothTransition?: boolean;
    
    smoothFactor?: number;
    
    enableHistory?: boolean;
    
    historySize?: number;
}

export interface BandwidthRequest {
    
    id: string;
    
    size: number;
    
    priority: DataPriority;
    
    maxDelay: number;
    
    timestamp: number;
    
    allocated: boolean;
    
    allocatedBandwidth: number;
}

export interface BandwidthControllerConfig {
    
    maxUploadBandwidth?: number;
    
    maxDownloadBandwidth?: number;
    
    strategy?: BandwidthControlStrategy;
    
    targetUsage?: number;
    
    priorityAllocation?: Record<DataPriority, number>;
    
    autoAdjust?: boolean;
    
    adjustInterval?: number;
}

export interface BandwidthTestConfig {
    
    serverUrl: string;
    
    uploadSize?: number;
    
    downloadSize?: number;
    
    timeout?: number;
    
    retries?: number;
    
    detailedLogging?: boolean;
    
    concurrentConnections?: number;
    
    useWebSocket?: boolean;
    
    useHttp?: boolean;
    
    testUpload?: boolean;
    
    testDownload?: boolean;
    
    testLatency?: boolean;
    
    latencyTestCount?: number;
}

export interface BandwidthTestResult {
    
    uploadBandwidth: number;
    
    downloadBandwidth: number;
    
    latency: number;
    
    jitter: number;
    
    success: boolean;
    
    error?: string;
    
    startTime: number;
    
    endTime: number;
    
    duration: number;
    
    uploadDetails?: {
        
        bytes: number;
        
        time: number;
        
        speed: number;
    };
    
    downloadDetails?: {
        
        bytes: number;
        
        time: number;
        
        speed: number;
    };
    
    latencyDetails?: {
        
        min: number;
        
        max: number;
        
        avg: number;
        
        stdDev: number;
        
        values: number[];
    };
}

export interface NetworkEntityComponentProps {
    
    entityId: string;
    
    type?: NetworkEntityType;
    
    ownerId: string;
    
    syncMode?: NetworkEntitySyncMode;
    
    ownershipMode?: NetworkEntityOwnershipMode;
    
    syncInterval?: number;
    
    autoSync?: boolean;
    
    isLocallyOwned?: boolean;
    
    canTransferOwnership?: boolean;
    
    syncPriority?: number;
    
    syncDistance?: number;
    
    syncGroup?: string;
    
    syncTags?: string[];
    
    customData?: Record<string, any>;
}

export interface NetworkTransformComponentProps {
    
    syncInterval?: number;
    
    autoSync?: boolean;
    
    syncPosition?: boolean;
    
    syncRotation?: boolean;
    
    syncScale?: boolean;
    
    positionLerpSpeed?: number;
    
    rotationLerpSpeed?: number;
    
    scaleLerpSpeed?: number;
    
    positionThreshold?: number;
    
    rotationThreshold?: number;
    
    scaleThreshold?: number;
    
    useCompression?: boolean;
    
    usePrediction?: boolean;
    
    useSmoothing?: boolean;
    
    useExtrapolation?: boolean;
    
    extrapolationTime?: number;
    
    maxExtrapolationDistance?: number;
    
    syncPriority?: number;
}

export interface NetworkUserComponentProps {
    
    userId: string;
    
    username: string;
    
    displayName?: string;
    
    avatarUrl?: string;
    
    state?: NetworkUserState;
    
    role?: NetworkUserRole;
    
    isLocal?: boolean;
    
    customData?: Record<string, any>;
}

export interface IncrementalCompressionOptions {
    
    enabled?: boolean;
    
    maxDepth?: number;
    
    includePathInfo?: boolean;
    
    useBinaryDiff?: boolean;
    
    compressIncrementalData?: boolean;
    
    useFieldFiltering?: boolean;
    
    includedFields?: string[];
    
    excludedFields?: string[];
}

export interface CompressionOptions {
    
    algorithm?: CompressionAlgorithm;
    
    level?: CompressionLevel;
    
    adaptive?: boolean;
    
    minSize?: number;
    
    useBinaryFormat?: boolean;
    
    useTypedArrayOptimization?: boolean;
    
    useDictionaryCompression?: boolean;
    
    compressionDictionary?: Uint8Array;
    
    incremental?: IncrementalCompressionOptions;
    
    customCompressFunction?: (data: any) => string | Uint8Array;
    
    customDecompressFunction?: (data: string | Uint8Array) => any;
}

export interface IncrementalCompressionResult {
    
    isIncremental: boolean;
    
    version: number;
    
    isComplete?: boolean;
    
    isEmpty?: boolean;
    
    paths?: string[];
    
    changedFieldsCount?: number;
    
    incrementalSize?: number;
    
    fullSize?: number;
    
    savedBytes?: number;
    
    savingsPercentage?: number;
}

export interface CompressionResult {
    
    data: string | Uint8Array;
    
    originalSize: number;
    
    compressedSize: number;
    
    ratio: number;
    
    algorithm: CompressionAlgorithm;
    
    level: CompressionLevel;
    
    time: number;
    
    isBinary?: boolean;
    
    usedDictionary?: boolean;
    
    incremental?: IncrementalCompressionResult;
}

export interface EnhancedNetworkSystemConfig {
    
    enabled?: boolean;
    
    localUserId?: string;
    
    syncInterval?: number;
    
    maxReconnectAttempts?: number;
    
    reconnectInterval?: number;
    
    enableCompression?: boolean;
    
    compressionAlgorithm?: CompressionAlgorithm;
    
    compressionLevel?: CompressionLevel;
    
    enableMediaStream?: boolean;
    
    enableAudio?: boolean;
    
    enableVideo?: boolean;
    
    enableScreenShare?: boolean;
    
    enableNetworkQualityMonitor?: boolean;
    
    enableBandwidthControl?: boolean;
    
    bandwidthControlStrategy?: BandwidthControlStrategy;
    
    maxUploadBandwidth?: number;
    
    maxDownloadBandwidth?: number;
    
    enableEntitySync?: boolean;
    
    enableUserSessionManagement?: boolean;
    
    defaultUserRole?: UserRole;
    
    enablePermissionCheck?: boolean;
    
    enableEventBuffer?: boolean;
    
    enableEventLogging?: boolean;
    
    enableServiceDiscovery?: boolean;
    
    serviceRegistryUrl?: string;
    
    enableMicroserviceClient?: boolean;
    
    apiGatewayUrl?: string;
    
    useApiGateway?: boolean;
    
    enablePrediction?: boolean;
    
    predictionAlgorithm?: PredictionAlgorithm;
    
    maxPredictionTime?: number;
    
    enableInterpolation?: boolean;
    
    interpolationFactor?: number;
    
    enableSpatialPartitioning?: boolean;
    
    spatialPartitioningMaxDepth?: number;
    
    spatialPartitioningMaxEntities?: number;
    
    enableAdaptiveControl?: boolean;
    
    adaptiveStrategy?: AdaptiveStrategy;
    
    enableJitterBuffer?: boolean;
    
    jitterBufferSize?: number;
    
    enablePrioritySync?: boolean;
    
    enableDeltaSync?: boolean;
}

export interface SyncAreaConfig {
    
    type: SyncAreaType;
    
    id: string;
    
    position?: {
        x: number;
        y: number;
        z: number;
    };
    
    radius?: number;
    
    bounds?: {
        min: {
            x: number;
            y: number;
            z: number;
        };
        max: {
            x: number;
            y: number;
            z: number;
        };
    };
    
    priority?: number;
    
    syncInterval?: number;
    
    enabled?: boolean;
}

export interface EntitySyncConfig {
    
    defaultSyncInterval?: number;
    
    minSyncInterval?: number;
    
    maxSyncInterval?: number;
    
    syncDistance?: number;
    
    useSpatialPartitioning?: boolean;
    
    spatialCellSize?: number;
    
    useInterpolation?: boolean;
    
    useExtrapolation?: boolean;
    
    extrapolationTime?: number;
    
    useCompression?: boolean;
    
    useDeltaSync?: boolean;
    
    usePrioritySync?: boolean;
    
    useAdaptiveSync?: boolean;
}

export interface EntitySyncState {
    
    entityId: string;
    
    ownerId: string;
    
    lastSyncTime: number;
    
    syncInterval: number;
    
    syncPriority: number;
    
    syncAreaId: string | null;
    
    needsSync: boolean;
    
    syncDataSize: number;
    
    syncCount: number;
    
    syncFailCount: number;
}

export interface VideoConstraints extends MediaTrackConstraints {
    
    width?: ConstrainULong;
    
    height?: ConstrainULong;
    
    frameRate?: ConstrainDouble;
    
    deviceId?: ConstrainDOMString;
    
    facingMode?: ConstrainDOMString;
}

export interface AudioConstraints extends MediaTrackConstraints {
    
    deviceId?: ConstrainDOMString;
    
    autoGainControl?: ConstrainBoolean;
    
    echoCancellation?: ConstrainBoolean;
    
    noiseSuppression?: ConstrainBoolean;
    
    sampleRate?: ConstrainULong;
    
    sampleSize?: ConstrainULong;
    
    channelCount?: ConstrainULong;
}

export interface CustomMediaStreamConstraints {
    
    audio?: boolean | AudioConstraints;
    
    video?: boolean | VideoConstraints;
    
    screen?: boolean | {
        video?: boolean | VideoConstraints;
        audio?: boolean | AudioConstraints;
    };
}

export interface MediaStreamConfig {
    
    audioConstraints?: MediaTrackConstraints;
    
    videoConstraints?: MediaTrackConstraints;
    
    screenShareConstraints?: MediaTrackConstraints;
    
    echoCancellation?: boolean;
    
    noiseSuppression?: boolean;
    
    autoGainControl?: boolean;
    
    stereo?: boolean;
    
    autoPlay?: boolean;
    
    muted?: boolean;
    
    mirror?: boolean;
    
    videoQuality?: MediaStreamQuality;
    
    frameRate?: number;
    
    width?: number;
    
    height?: number;
    
    videoBitrate?: number;
    
    audioBitrate?: number;
}

export interface MediaStreamInfo {
    
    id: string;
    
    type: MediaStreamType;
    
    quality: MediaStreamQuality;
    
    stream: MediaStream;
    
    audioTrack?: MediaStreamTrack;
    
    videoTrack?: MediaStreamTrack;
    
    enabled: boolean;
    
    muted: boolean;
    
    paused: boolean;
    
    createdAt: number;
    
    config: MediaStreamConfig;
    
    userId?: string;
    
    customData?: Record<string, any>;
}

export interface MediaStreamManagerConfig {
    
    defaultAudioConstraints?: MediaTrackConstraints;
    
    defaultVideoConstraints?: MediaTrackConstraints;
    
    defaultScreenShareConstraints?: MediaTrackConstraints;
    
    enableDeviceEnumeration?: boolean;
    
    enableDeviceChangeDetection?: boolean;
    
    enableAudioProcessing?: boolean;
    
    enableVideoProcessing?: boolean;
    
    enableAutoPlay?: boolean;
    
    enableAudioLevelMonitoring?: boolean;
    
    audioLevelMonitoringInterval?: number;
}

export interface MediaDeviceInfo {
    
    deviceId: string;
    
    label: string;
    
    kind: MediaDeviceKind;
    
    groupId: string;
}

export interface MicroserviceClientConfig {
    
    serviceDiscoveryClient?: ServiceDiscoveryClient;
    
    apiGatewayUrl?: string;
    
    useApiGateway?: boolean;
    
    useServiceDiscovery?: boolean;
    
    requestTimeout?: number;
    
    retryCount?: number;
    
    retryInterval?: number;
    
    enableRequestCache?: boolean;
    
    requestCacheTime?: number;
    
    authToken?: string;
}

export interface RequestOptions {
    
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    
    headers?: Record<string, string>;
    
    body?: any;
    
    useCache?: boolean;
    
    cacheTime?: number;
    
    timeout?: number;
    
    retryCount?: number;
    
    retryInterval?: number;
}

export interface NetworkParamsConfig {
    
    syncInterval: number;
    
    compressionLevel: number;
    
    useDeltaSync: boolean;
    
    usePrediction: boolean;
    
    predictionTime: number;
    
    useInterpolation: boolean;
    
    interpolationFactor: number;
    
    usePrioritySync: boolean;
    
    useSpatialPartitioning: boolean;
    
    useJitterBuffer: boolean;
    
    jitterBufferSize: number;
    
    maxUploadBandwidth: number;
    
    maxDownloadBandwidth: number;
}

export interface NetworkAdaptiveControllerConfig {
    
    strategy?: AdaptiveStrategy;
    
    adjustInterval?: number;
    
    enableAutoAdjust?: boolean;
    
    enableHistory?: boolean;
    
    historySize?: number;
    
    enableDebugLog?: boolean;
    
    enableSmoothTransition?: boolean;
    
    smoothFactor?: number;
    
    enablePredictiveAdjustment?: boolean;
    
    predictiveWindowSize?: number;
}

export interface NetworkIssue {
    
    type: NetworkIssueType;
    
    severity: number;
    
    description: string;
    
    solution: string;
    
    startTime: number;
    
    duration: number;
    
    resolved: boolean;
}

export interface NetworkQualityData {
    
    rtt: number;
    
    packetLoss: number;
    
    jitter: number;
    
    bandwidth: number;
    
    uploadBandwidth?: number;
    
    downloadBandwidth?: number;
    
    stability?: number;
    
    congestion?: number;
    
    level: NetworkQualityLevel;
    
    timestamp: number;
    
    issues?: NetworkIssue[];
    
    bandwidthUtilization?: number;
    
    qualityScore?: number;
    
    reliability?: number;
    
    latencyTrend?: number;
    
    networkType?: string;
    
    connectionType?: string;
    
    signalStrength?: number;
    
    hopCount?: number;
    
    serverResponseTime?: number;
    
    connectionEstablishTime?: number;
    
    dataTransferRate?: number;
    
    interfaceStatus?: string;
    
    dnsResolutionTime?: number;
    
    errorCount?: number;
    
    warningCount?: number;
    
    prediction?: {
        
        rtt?: number;
        
        packetLoss?: number;
        
        bandwidth?: number;
        
        stability?: number;
        
        confidence?: number;
    };
}

export interface NetworkParamsAdjustment {
    
    timestamp: number;
    
    networkQuality: NetworkQualityData;
    
    beforeParams: NetworkParamsConfig;
    
    afterParams: NetworkParamsConfig;
    
    reason: string;
}

export interface NetworkEntity {
    
    entityId: string;
    
    type?: NetworkEntityType;
    
    name?: string;
    
    tags?: string[];
    
    layer?: number;
    
    ownerId: string;
    
    syncMode?: NetworkEntitySyncMode;
    
    ownershipMode?: NetworkEntityOwnershipMode;
    
    data: any;
    
    createTime: number;
    
    updateTime: number;
    
    parentId?: string;
    
    childIds?: string[];
    
    position?: {
        x: number;
        y: number;
        z: number;
    };
    
    rotation?: {
        x: number;
        y: number;
        z: number;
        w: number;
    };
    
    scale?: {
        x: number;
        y: number;
        z: number;
    };
    
    velocity?: {
        x: number;
        y: number;
        z: number;
    };
    
    acceleration?: {
        x: number;
        y: number;
        z: number;
    };
    
    angularVelocity?: {
        x: number;
        y: number;
        z: number;
    };
    
    visible?: boolean;
    
    enabled?: boolean;
    
    isStatic?: boolean;
    
    isTrigger?: boolean;
    
    isPhysics?: boolean;
    
    collider?: {
        type: string;
        size?: {
            x: number;
            y: number;
            z: number;
        };
        radius?: number;
        height?: number;
        center?: {
            x: number;
            y: number;
            z: number;
        };
    };
    
    renderer?: {
        type: string;
        material?: string;
        mesh?: string;
        color?: {
            r: number;
            g: number;
            b: number;
            a: number;
        };
        castShadow?: boolean;
        receiveShadow?: boolean;
    };
    
    animation?: {
        clip?: string;
        speed?: number;
        loop?: boolean;
        playing?: boolean;
        time?: number;
    };
    
    audio?: {
        clip?: string;
        volume?: number;
        pitch?: number;
        loop?: boolean;
        playing?: boolean;
        spatial?: boolean;
        minDistance?: number;
        maxDistance?: number;
    };
    
    script?: {
        [key: string]: any;
    };
    
    customData?: {
        [key: string]: any;
    };
    
    metadata?: {
        [key: string]: any;
    };
}

export interface NetworkEventBufferConfig {
    
    maxBufferSize?: number;
    
    processInterval?: number;
    
    autoProcess?: boolean;
    
    maxEventsPerProcess?: number;
    
    eventTypePriorities?: Record<string, EventPriority>;
}

export interface NetworkEventDispatcherConfig {
    
    useEventBuffer?: boolean;
    
    eventBufferConfig?: {
        
        maxBufferSize?: number;
        
        processInterval?: number;
        
        autoProcess?: boolean;
        
        maxEventsPerProcess?: number;
    };
    
    enableEventLogging?: boolean;
    
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    
    allowDefaultHandlers?: boolean;
}

export interface NetworkMessage {
    
    type: string;
    
    data: any;
    
    senderId?: string;
    
    receiverId?: string;
    
    timestamp: number;
    
    id?: string;
    
    requireAck?: boolean;
    
    ackId?: string;
    
    priority?: number;
    
    expireTime?: number;
    
    sequence?: number;
    
    reliable?: boolean;
    
    retryCount?: number;
    
    maxRetries?: number;
    
    retryInterval?: number;
    
    compressed?: boolean;
    
    encrypted?: boolean;
    
    encryptionAlgorithm?: string;
    
    signature?: string;
    
    version?: string;
    
    metadata?: Record<string, any>;
}

export interface PredictionConfig {
    
    algorithm?: PredictionAlgorithm;
    
    maxPredictionTime?: number;
    
    useSmoothing?: boolean;
    
    smoothingFactor?: number;
    
    useAdaptivePrediction?: boolean;
    
    useJitterBuffer?: boolean;
    
    jitterBufferSize?: number;
}

export interface PredictionState {
    
    position: THREE.Vector3;
    
    rotation: THREE.Quaternion;
    
    velocity: THREE.Vector3;
    
    angularVelocity: THREE.Vector3;
    
    acceleration: THREE.Vector3;
    
    angularAcceleration: THREE.Vector3;
    
    timestamp: number;
}

export interface NetworkQualityMonitorConfig {
    
    sampleInterval?: number;
    
    historyDuration?: number;
    
    autoSample?: boolean;
    
    rttThresholds?: {
        excellent: number;
        good: number;
        medium: number;
        bad: number;
    };
    
    packetLossThresholds?: {
        excellent: number;
        good: number;
        medium: number;
        bad: number;
    };
    
    jitterThresholds?: {
        excellent: number;
        good: number;
        medium: number;
        bad: number;
    };
    
    bandwidthThresholds?: {
        excellent: number;
        good: number;
        medium: number;
        bad: number;
    };
    
    enableDiagnostics?: boolean;
    
    diagnosticsInterval?: number;
    
    detailedLogging?: boolean;
    
    continuousMeasurement?: boolean;
    
    autoTroubleshoot?: boolean;
    
    historySize?: number;
    
    pingInterval?: number;
    
    enableAdvancedDiagnostics?: boolean;
    
    enablePathAnalysis?: boolean;
    
    enableNetworkTypeDetection?: boolean;
    
    enableConnectionTypeDetection?: boolean;
    
    enableSignalStrengthDetection?: boolean;
    
    enableDnsMonitoring?: boolean;
    
    enableServerResponseTimeMonitoring?: boolean;
    
    enableInterfaceMonitoring?: boolean;
    
    enableNetworkAddressMonitoring?: boolean;
    
    enableErrorCounting?: boolean;
    
    enableWarningCounting?: boolean;
    
    enableNetworkPrediction?: boolean;
    
    predictionWindowSize?: number;
    
    enableNetworkEventLogging?: boolean;
    
    enableReportGeneration?: boolean;
    
    reportGenerationInterval?: number;
}

export interface NetworkSecuritySystemConfig {
    
    debug?: boolean;
    
    defaultEncryptionAlgorithm?: EncryptionAlgorithm;
    
    defaultHashAlgorithm?: HashAlgorithm;
    
    enableEndToEndEncryption?: boolean;
    
    enableSecureKeyExchange?: boolean;
    
    enableMessageSigning?: boolean;
    
    enableSessionManagement?: boolean;
    
    sessionTimeout?: number;
    
    enableAccessControl?: boolean;
    
    enableAuditLog?: boolean;
    
    enableReplayProtection?: boolean;
    
    enableSecureTokens?: boolean;
    
    tokenExpiration?: number;
    
    enableCertificateValidation?: boolean;
    
    certificatePath?: string;
}

export interface NetworkSimulatorConfig {
    
    enabled?: boolean;
    
    latency?: number;
    
    latencyJitter?: number;
    
    packetLoss?: number;
    
    bandwidthLimit?: number;
    
    enableRandomDisconnect?: boolean;
    
    disconnectProbability?: number;
    
    reconnectTime?: number;
    
    detailedLogging?: boolean;
}

export interface NetworkSystemOptions {
    
    autoConnect?: boolean;
    
    serverUrl?: string;
    
    roomId?: string;
    
    userId?: string;
    
    username?: string;
    
    enableWebRTC?: boolean;
    
    iceServers?: RTCIceServer[];
    
    maxReconnectAttempts?: number;
    
    reconnectInterval?: number;
    
    syncInterval?: number;
    
    enableCompression?: boolean;
    
    enableMediaStream?: boolean;
    
    enableAudio?: boolean;
    
    enableVideo?: boolean;
    
    enableScreenShare?: boolean;
    
    enableNetworkQualityMonitor?: boolean;
    
    enableBandwidthControl?: boolean;
    
    bandwidthControlStrategy?: BandwidthControlStrategy;
    
    maxUploadBandwidth?: number;
    
    maxDownloadBandwidth?: number;
    
    compressionAlgorithm?: CompressionAlgorithm;
    
    compressionLevel?: CompressionLevel;
    
    enableEntitySync?: boolean;
    
    enableUserSessionManagement?: boolean;
    
    defaultUserRole?: UserRole;
    
    enablePermissionCheck?: boolean;
    
    enableEventBuffer?: boolean;
    
    enableEventLogging?: boolean;
    
    enableServiceDiscovery?: boolean;
    
    serviceRegistryUrl?: string;
    
    enableMicroserviceClient?: boolean;
    
    apiGatewayUrl?: string;
    
    useApiGateway?: boolean;
}

export interface NetworkTracerConfig {
    
    targetHost: string;
    
    maxHops?: number;
    
    hopTimeout?: number;
    
    hopRetries?: number;
    
    detailedLogging?: boolean;
    
    resolveHostnames?: boolean;
    
    geoLocation?: boolean;
    
    analyzeRouteQuality?: boolean;
    
    detectBottlenecks?: boolean;
    
    useIcmp?: boolean;
    
    useUdp?: boolean;
    
    useTcp?: boolean;
}

export interface RouteNode {
    
    hop: number;
    
    ip: string;
    
    hostname?: string;
    
    responseTime: number;
    
    packetLoss: number;
    
    location?: {
        
        country?: string;
        
        city?: string;
        
        longitude?: number;
        
        latitude?: number;
        
        isp?: string;
    };
    
    isBottleneck?: boolean;
    
    bottleneckReason?: string;
    
    qualityScore?: number;
}

export interface NetworkTraceResult {
    
    targetHost: string;
    
    targetIp?: string;
    
    success: boolean;
    
    error?: string;
    
    startTime: number;
    
    endTime: number;
    
    duration: number;
    
    hops: number;
    
    nodes: RouteNode[];
    
    endToEndLatency: number;
    
    routeQualityScore?: number;
    
    bottleneckNodeIndices?: number[];
    
    routeAnalysis?: {
        
        avgHopLatency: number;
        
        maxHopLatency: number;
        
        maxHopLatencyIndex: number;
        
        avgPacketLoss: number;
        
        maxPacketLoss: number;
        
        maxPacketLossIndex: number;
        
        internationalHops: number;
        
        crossIspHops: number;
    };
}

export interface NetworkUser {
    
    userId: string;
    
    username: string;
    
    displayName?: string;
    
    avatarUrl?: string;
    
    state?: NetworkUserState;
    
    role?: NetworkUserRole;
    
    joinTime: number;
    
    lastActiveTime?: number;
    
    ipAddress?: string;
    
    deviceInfo?: string;
    
    location?: {
        x: number;
        y: number;
        z: number;
    };
    
    rotation?: {
        x: number;
        y: number;
        z: number;
        w: number;
    };
    
    scale?: {
        x: number;
        y: number;
        z: number;
    };
    
    velocity?: {
        x: number;
        y: number;
        z: number;
    };
    
    acceleration?: {
        x: number;
        y: number;
        z: number;
    };
    
    inputState?: {
        [key: string]: any;
    };
    
    customData?: {
        [key: string]: any;
    };
    
    metadata?: {
        [key: string]: any;
    };
}

export interface NetworkProtocolOptions {
    
    autoReconnect?: boolean;
    
    maxReconnectAttempts?: number;
    
    reconnectInterval?: number;
    
    heartbeatInterval?: number;
    
    heartbeatTimeout?: number;
    
    connectionTimeout?: number;
    
    enableCompression?: boolean;
    
    enableEncryption?: boolean;
    
    encryptionKey?: string;
    
    enableDebugLog?: boolean;
    
    [key: string]: any;
}

export interface NetworkProtocolStats {
    
    bytesSent: number;
    
    bytesReceived: number;
    
    messagesSent: number;
    
    messagesReceived: number;
    
    messagesLost: number;
    
    averageRtt: number;
    
    minRtt: number;
    
    maxRtt: number;
    
    connectionTime: number;
    
    state: NetworkProtocolState;
    
    reconnectCount: number;
    
    lastActivityTime: number;
}

export interface UDPProtocolOptions extends NetworkProtocolOptions {
    
    iceServers?: RTCIceServer[];
    
    signalingServerUrl?: string;
    
    maxMessageSize?: number;
    
    ordered?: boolean;
    
    maxRetransmits?: number;
}

export interface ServerConnectionTestConfig {
    
    serverUrl: string;
    
    timeout?: number;
    
    retries?: number;
    
    detailedLogging?: boolean;
    
    testWebSocket?: boolean;
    
    testHttp?: boolean;
    
    testAvailability?: boolean;
    
    testResponseTime?: boolean;
    
    testServerStatus?: boolean;
    
    testDnsResolution?: boolean;
    
    testSslCertificate?: boolean;
    
    testGeoLocation?: boolean;
    
    testRouteTrace?: boolean;
}

export interface ServerConnectionTestResult {
    
    success: boolean;
    
    error?: string;
    
    startTime: number;
    
    endTime: number;
    
    duration: number;
    
    available?: boolean;
    
    responseTime?: number;
    
    webSocketTest?: {
        
        success: boolean;
        
        connectionTime: number;
        
        error?: string;
    };
    
    httpTest?: {
        
        success: boolean;
        
        responseTime: number;
        
        statusCode: number;
        
        error?: string;
    };
    
    serverStatusTest?: {
        
        success: boolean;
        
        status: string;
        
        version?: string;
        
        load?: number;
        
        error?: string;
    };
    
    dnsResolutionTest?: {
        
        success: boolean;
        
        resolutionTime: number;
        
        ipAddress?: string;
        
        error?: string;
    };
    
    sslCertificateTest?: {
        
        success: boolean;
        
        valid: boolean;
        
        expiryDate?: Date;
        
        issuer?: string;
        
        error?: string;
    };
    
    geoLocationTest?: {
        
        success: boolean;
        
        country?: string;
        
        city?: string;
        
        longitude?: number;
        
        latitude?: number;
        
        error?: string;
    };
    
    routeTraceTest?: {
        
        success: boolean;
        
        hops: number;
        
        nodes: Array<{
            
            ip: string;
            
            responseTime: number;
            
            location?: string;
        }>;
        
        error?: string;
    };
}

export interface ServiceDiscoveryClientConfig {
    
    registryUrl?: string;
    
    heartbeatInterval?: number;
    
    discoveryCache?: number;
    
    enableAutoHeartbeat?: boolean;
    
    enableDiscoveryCache?: boolean;
    
    retryCount?: number;
    
    retryInterval?: number;
}

export interface ServiceInstance {
    
    serviceName: string;
    
    instanceId: string;
    
    host: string;
    
    port: number;
    
    secure: boolean;
    
    metadata?: Record<string, any>;
    
    status: 'UP' | 'DOWN' | 'UNKNOWN';
    
    registrationTime: number;
    
    lastHeartbeatTime: number;
}

export interface QuadtreeConfig {
    
    maxDepth?: number;
    
    maxEntities?: number;
    
    minNodeSize?: number;
    
    worldSize?: number;
    
    worldCenter?: THREE.Vector3;
    
    enableDynamicAdjustment?: boolean;
    
    enableLooseQuadtree?: boolean;
    
    looseFactor?: number;
}

export interface SyncPriorityConfig {
    
    useDistancePriority?: boolean;
    
    distancePriorityWeight?: number;
    
    maxDistance?: number;
    
    useVisibilityPriority?: boolean;
    
    visibilityPriorityWeight?: number;
    
    visibilityAngle?: number;
    
    useImportancePriority?: boolean;
    
    importancePriorityWeight?: number;
    
    useActivityPriority?: boolean;
    
    activityPriorityWeight?: number;
    
    activityThreshold?: number;
    
    activityDecayTime?: number;
    
    useCustomPriority?: boolean;
    
    customPriorityWeight?: number;
    
    customPriorityFunction?: (entity: Entity, observerPosition: Vector3) => number;
    
    priorityUpdateInterval?: number;
    
    priorityRange?: [number, number];
    
    useAdaptiveSync?: boolean;
    
    minSyncInterval?: number;
    
    maxSyncInterval?: number;
    
    baseSyncInterval?: number;
    
    priorityToIntervalFactor?: number;
}

export interface UserSession {
    
    userId: string;
    
    username: string;
    
    role: UserRole;
    
    permissions: Set<UserPermission>;
    
    connectionTime: number;
    
    lastActivityTime: number;
    
    isOnline: boolean;
    
    isAuthenticated: boolean;
    
    sessionToken?: string;
    
    clientInfo?: any;
}

export interface UserSessionManagerConfig {
    
    sessionTimeout?: number;
    
    enableSessionTimeout?: boolean;
    
    enablePermissionCheck?: boolean;
    
    defaultRole?: UserRole;
    
    rolePermissions?: Record<UserRole, UserPermission[]>;
    
    allowAnonymous?: boolean;
    
    maxUsers?: number;
}

export interface WebRTCConnectionConfig {
    
    enableDataChannel?: boolean;
    
    enableAudio?: boolean;
    
    enableVideo?: boolean;
    
    enableScreenShare?: boolean;
    
    dataChannelConfig?: RTCDataChannelInit;
    
    audioConstraints?: MediaTrackConstraints;
    
    videoConstraints?: MediaTrackConstraints;
    
    useCompression?: boolean;
}

export interface WebRTCConnectionManagerConfig {
    
    iceServers?: RTCIceServer[];
    
    enableDataChannel?: boolean;
    
    enableAudio?: boolean;
    
    enableVideo?: boolean;
    
    enableScreenShare?: boolean;
    
    dataChannelConfig?: RTCDataChannelInit;
    
    audioConstraints?: MediaTrackConstraints;
    
    videoConstraints?: MediaTrackConstraints;
    
    useCompression?: boolean;
    
    maxReconnectAttempts?: number;
    
    reconnectInterval?: number;
    
    heartbeatInterval?: number;
    
    connectionTimeout?: number;
    
    autoReconnect?: boolean;
    
    useNetworkQualityMonitor?: boolean;
    
    useBandwidthController?: boolean;
}

export interface WebRTCDataChannelOptions {
    
    label: string;
    
    ordered?: boolean;
    
    maxRetransmits?: number;
    
    maxRetransmitTime?: number;
    
    protocol?: string;
    
    negotiated?: boolean;
    
    id?: number;
}

export interface WebRTCDataChannelEvents {
    
    open: () => void;
    
    close: () => void;
    
    error: (error: Event) => void;
    
    message: (data: any) => void;
}

export interface ParticleEmitterOptions {
    
    name?: string;
    
    shapeType?: EmitterShapeType;
    
    shapeParams?: any;
    
    texture?: THREE.Texture | string;
    
    material?: THREE.Material;
    
    particleSize?: number | [number, number];
    
    particleColor?: THREE.Color | [THREE.Color, THREE.Color];
    
    particleOpacity?: number | [number, number];
    
    particleLifetime?: number | [number, number];
    
    particleVelocity?: number | [number, number];
    
    particleAcceleration?: THREE.Vector3;
    
    particleRotationSpeed?: number | [number, number];
    
    particleScaleSpeed?: THREE.Vector2 | [THREE.Vector2, THREE.Vector2];
    
    emissionRate?: number;
    
    emissionAngle?: number | [number, number];
    
    emissionForce?: number | [number, number];
    
    emissionDirection?: THREE.Vector3;
    
    emissionSpread?: number;
    
    gravity?: THREE.Vector3;
    
    drag?: number;
    
    enableCollision?: boolean;
    
    enableSorting?: boolean;
    
    autoStart?: boolean;
    
    duration?: number;
    
    maxParticles?: number;
    
    loop?: boolean;
    
    burst?: {
        
        count: number;
        
        interval: number;
        
        cycles: number;
    };
}

export interface ParticleSystemOptions {
    
    maxParticles?: number;
    
    useGPU?: boolean;
    
    enableCollision?: boolean;
    
    enablePhysics?: boolean;
    
    enableSorting?: boolean;
}

export interface CCDOptions {
    
    maxSubSteps?: number;
    
    minSubStepTime?: number;
    
    velocityThreshold?: number;
    
    enableForAll?: boolean;
}

export interface CollisionEventData {
    
    type: CollisionEventType;
    
    entityA: Entity;
    
    entityB: Entity;
    
    contactPoint: THREE.Vector3;
    
    contactNormal: THREE.Vector3;
    
    impulse: number;
    
    relativeVelocity: THREE.Vector3;
    
    time: number;
    
    cannonEvent?: any;
}

export interface PhysicsBodyOptions {
    
    type?: BodyType;
    
    mass?: number;
    
    position?: THREE.Vector3;
    
    quaternion?: THREE.Quaternion;
    
    linearDamping?: number;
    
    angularDamping?: number;
    
    allowSleep?: boolean;
    
    sleepSpeedLimit?: number;
    
    sleepTimeLimit?: number;
    
    collisionFilterGroup?: number;
    
    collisionFilterMask?: number;
    
    material?: CANNON.Material;
    
    fixedRotation?: boolean;
    
    autoUpdateTransform?: boolean;
}

export interface ColliderShape {
    
    shape: CANNON.Shape;
    
    offset: CANNON.Vec3;
    
    orientation: CANNON.Quaternion;
}

export interface ColliderOptions {
    
    type: ColliderType;
    
    params?: any;
    
    offset?: THREE.Vector3;
    
    orientation?: THREE.Quaternion;
    
    isTrigger?: boolean;
}

export interface PhysicsConstraintOptions {
    
    type: ConstraintType;
    
    entityA: Entity;
    
    entityB?: Entity;
    
    pivotA?: THREE.Vector3;
    
    pivotB?: THREE.Vector3;
    
    axisA?: THREE.Vector3;
    
    axisB?: THREE.Vector3;
    
    distance?: number;
    
    stiffness?: number;
    
    damping?: number;
    
    maxAngle?: number;
    
    minAngle?: number;
    
    enableMotor?: boolean;
    
    motorSpeed?: number;
    
    motorMaxForce?: number;
    
    collideConnected?: boolean;
}

export interface PhysicsWorldOptions {
    
    gravity?: THREE.Vector3;
    
    allowSleep?: boolean;
    
    iterations?: number;
    
    broadphase?: 'naive' | 'sap' | 'grid';
    
    gridBroadphaseSize?: number;
    
    defaultContactMaterial?: CANNON.ContactMaterial;
    
    defaultFriction?: number;
    
    defaultRestitution?: number;
}

export interface ConeTwistConstraintOptions {
    
    pivotA?: THREE.Vector3;
    
    pivotB?: THREE.Vector3;
    
    axisA?: THREE.Vector3;
    
    axisB?: THREE.Vector3;
    
    maxForce?: number;
    
    collideConnected?: boolean;
    
    angle?: number;
    
    twistAngle?: number;
}

export interface DistanceConstraintOptions {
    
    distance?: number;
    
    maxForce?: number;
    
    collideConnected?: boolean;
}

export interface FixedConstraintOptions {
    
    collideConnected?: boolean;
    
    maxForce?: number;
}

export interface HingeConstraintOptions {
    
    pivotA?: THREE.Vector3;
    
    pivotB?: THREE.Vector3;
    
    axisA?: THREE.Vector3;
    
    axisB?: THREE.Vector3;
    
    maxForce?: number;
    
    collideConnected?: boolean;
    
    motorEnabled?: boolean;
    
    motorSpeed?: number;
    
    motorMaxForce?: number;
    
    angleEnabled?: boolean;
    
    angleMin?: number;
    
    angleMax?: number;
}

export interface LockConstraintOptions {
    
    maxForce?: number;
    
    collideConnected?: boolean;
}

export interface PointToPointConstraintOptions {
    
    pivotA?: THREE.Vector3;
    
    pivotB?: THREE.Vector3;
    
    maxForce?: number;
    
    collideConnected?: boolean;
}

export interface SliderConstraintOptions {
    
    axisA?: THREE.Vector3;
    
    axisB?: THREE.Vector3;
    
    lowerLimit?: number;
    
    upperLimit?: number;
    
    collideConnected?: boolean;
    
    maxForce?: number;
}

export interface SpringConstraintOptions {
    
    localAnchorA?: THREE.Vector3;
    
    localAnchorB?: THREE.Vector3;
    
    restLength?: number;
    
    stiffness?: number;
    
    damping?: number;
    
    worldAxis?: THREE.Vector3;
    
    collideConnected?: boolean;
}

export interface WheelConstraintOptions {
    
    axisA?: THREE.Vector3;
    
    axisB?: THREE.Vector3;
    
    suspensionStiffness?: number;
    
    suspensionDamping?: number;
    
    suspensionLength?: number;
    
    suspensionMaxLength?: number;
    
    suspensionMinLength?: number;
    
    steeringAngle?: number;
    
    collideConnected?: boolean;
    
    maxForce?: number;
}

export interface PhysicsDebuggerOptions {
    
    showBodies?: boolean;
    
    showConstraints?: boolean;
    
    showContactPoints?: boolean;
    
    showAABBs?: boolean;
    
    bodyColor?: THREE.Color | number;
    
    staticBodyColor?: THREE.Color | number;
    
    kinematicBodyColor?: THREE.Color | number;
    
    constraintColor?: THREE.Color | number;
    
    contactPointColor?: THREE.Color | number;
    
    aabbColor?: THREE.Color | number;
    
    lineWidth?: number;
    
    opacity?: number;
}

export interface EnhancedPhysicsDebuggerOptions extends PhysicsDebuggerOptions {
    
    showVelocities?: boolean;
    
    showForces?: boolean;
    
    showCenterOfMass?: boolean;
    
    showSleepState?: boolean;
    
    showPerformanceStats?: boolean;
    
    showContactNormals?: boolean;
    
    showContactForces?: boolean;
    
    showFrictionForces?: boolean;
    
    velocityColor?: THREE.Color | number;
    
    forceColor?: THREE.Color | number;
    
    centerOfMassColor?: THREE.Color | number;
    
    sleepStateColor?: THREE.Color | number;
    
    contactNormalColor?: THREE.Color | number;
    
    contactForceColor?: THREE.Color | number;
    
    frictionForceColor?: THREE.Color | number;
    
    vectorScale?: number;
    
    contactForceScale?: number;
}

export interface PhysicsCharacterInteractionSystemConfig {
    
    debug?: boolean;
    
    maxInteractionDistance?: number;
    
    maxInteractionForce?: number;
    
    interactionForceDamping?: number;
    
    enableForceFeedback?: boolean;
    
    enableInteractionAnimation?: boolean;
}

export interface PhysicsInteractionComponentConfig {
    
    enabled?: boolean;
    
    maxInteractionDistance?: number;
    
    interactionForce?: number;
    
    interactionForceDamping?: number;
    
    allowedInteractionTypes?: InteractionType[];
    
    canBePushed?: boolean;
    
    canBePulled?: boolean;
    
    canBeLifted?: boolean;
    
    canBeThrown?: boolean;
    
    canBeClimbed?: boolean;
    
    canBeHanged?: boolean;
    
    onInteractionStart?: (entity: Entity, interactor: Entity, type: InteractionType) => void;
    
    onInteractionEnd?: (entity: Entity, interactor: Entity, type: InteractionType) => void;
}

export interface PhysicsInteractionConstraintConfig {
    
    constraintType?: string;
    
    constraintParams?: any;
    
    useSpring?: boolean;
    
    springStiffness?: number;
    
    springDamping?: number;
    
    maxForce?: number;
    
    collisionGroup?: number;
    
    collisionMask?: number;
}

export interface PhysicsSceneExportOptions {
    
    includeBodies?: boolean;
    
    includeColliders?: boolean;
    
    includeConstraints?: boolean;
    
    includeWorld?: boolean;
    
    includeMaterials?: boolean;
    
    prettyPrint?: boolean;
    
    includeMetadata?: boolean;
    
    metadata?: Record<string, any>;
}

export interface PhysicsSceneExportData {
    
    version: string;
    
    metadata?: Record<string, any>;
    
    world?: any;
    
    bodies?: any[];
    
    colliders?: any[];
    
    constraints?: any[];
    
    materials?: any[];
}

export interface PhysicsSceneImportOptions {
    
    importBodies?: boolean;
    
    importColliders?: boolean;
    
    importConstraints?: boolean;
    
    importWorld?: boolean;
    
    importMaterials?: boolean;
    
    clearExisting?: boolean;
    
    entityIdMap?: Map<string, string>;
}

export interface OctreeOptions {
    
    maxDepth?: number;
    
    maxObjects?: number;
    
    minNodeSize?: number;
    
    worldSize?: number;
    
    worldCenter?: CANNON.Vec3;
    
    useLooseOctree?: boolean;
    
    looseFactor?: number;
    
    useDebugVisualization?: boolean;
}

export interface OctreeNode {
    
    center: CANNON.Vec3;
    
    halfSize: number;
    
    depth: number;
    
    bodies: Set<CANNON.Body>;
    
    children: OctreeNode[] | null;
    
    aabb: CANNON.AABB;
    
    isLeaf: boolean;
}

export interface SpatialHashOptions {
    
    cellSize?: number;
    
    worldSize?: number;
    
    worldCenter?: CANNON.Vec3;
    
    useDynamicHash?: boolean;
    
    useDebugVisualization?: boolean;
}

export interface ISpatialPartitioning {
    
    add(body: CANNON.Body): void;
    
    remove(body: CANNON.Body): void;
    
    update(body: CANNON.Body): void;
    
    updateAll(): void;
    
    queryRegion(min: CANNON.Vec3, max: CANNON.Vec3): CANNON.Body[];
    
    queryRay(from: CANNON.Vec3, to: CANNON.Vec3): CANNON.Body[];
    
    querySphere(center: CANNON.Vec3, radius: number): CANNON.Body[];
    
    queryPotentialCollisions(body: CANNON.Body): CANNON.Body[];
    
    getBodies(): CANNON.Body[];
    
    getBodyCount(): number;
    
    clear(): void;
    
    dispose(): void;
    
    getDebugInfo(): any;
    
    getDebugMesh(): THREE.Object3D;
}

export interface CollisionPair {
    
    bodyA: CANNON.Body;
    
    bodyB: CANNON.Body;
}

export interface UniformGridOptions {
    
    cellSize?: number;
    
    worldSize?: number;
    
    worldCenter?: CANNON.Vec3;
    
    autoResize?: boolean;
    
    useDynamicGrid?: boolean;
    
    useDebugVisualization?: boolean;
}

export interface PhysicsSystemOptions {
    
    gravity?: {
        x: number;
        y: number;
        z: number;
    };
    
    updateFrequency?: number;
    
    allowSleep?: boolean;
    
    enableCCD?: boolean;
    
    ccdOptions?: CCDOptions;
    
    debug?: boolean;
    
    useEnhancedDebugger?: boolean;
    
    debuggerOptions?: any;
    
    iterations?: number;
}

export interface PhysicsBodyPreset {
    
    type: BodyType;
    
    mass: number;
    
    linearDamping: number;
    
    angularDamping: number;
    
    allowSleep: boolean;
    
    fixedRotation: boolean;
    
    materialName: string;
    
    enableCCD: boolean;
    
    collisionFilterGroup: number;
    
    collisionFilterMask: number;
}

export interface PhysicsColliderPreset {
    
    type: ColliderType;
    
    isTrigger: boolean;
    
    params: any;
    
    position?: THREE.Vector3;
    
    quaternion?: THREE.Quaternion;
    
    materialName: string;
}

export interface PhysicsConstraintPreset {
    
    type: string;
    
    collideConnected: boolean;
    
    params: any;
}

export interface PhysicsMaterialPreset {
    
    name: string;
    
    friction: number;
    
    restitution: number;
    
    contactEquationStiffness?: number;
    
    contactEquationRelaxation?: number;
    
    frictionEquationStiffness?: number;
    
    frictionEquationRelaxation?: number;
}

export interface PhysicsWorldPreset {
    
    gravity: THREE.Vector3;
    
    allowSleep: boolean;
    
    iterations: number;
    
    broadphase: 'naive' | 'sap' | 'grid';
    
    gridBroadphaseSize?: number;
    
    defaultFriction: number;
    
    defaultRestitution: number;
}

export interface PhysicsPreset {
    
    name: string;
    
    description: string;
    
    category: string;
    
    worldPreset?: PhysicsWorldPreset;
    
    bodyPreset?: PhysicsBodyPreset;
    
    colliderPreset?: PhysicsColliderPreset;
    
    constraintPreset?: PhysicsConstraintPreset;
    
    materialPresets?: PhysicsMaterialPreset[];
    
    customData?: any;
}

export interface CuttingPlane {
    
    normal: THREE.Vector3;
    
    point: THREE.Vector3;
}

export interface CuttingRay {
    
    origin: THREE.Vector3;
    
    direction: THREE.Vector3;
    
    length: number;
}

export interface SoftBodyCutterOptions {
    
    enabled?: boolean;
    
    tearingEnabled?: boolean;
    
    tearingThreshold?: number;
}

export interface SoftRigidInteractionOptions {
    
    enabled?: boolean;
    
    collisionRadius?: number;
    
    collisionResponse?: number;
    
    friction?: number;
    
    useSpatialPartitioning?: boolean;
}

export interface LODConfig {
    
    highDetailDistance: number;
    
    mediumDetailDistance: number;
    
    lowDetailDistance: number;
    
    highDetailResolution: {
        x: number;
        y: number;
    };
    
    mediumDetailResolution: {
        x: number;
        y: number;
    };
    
    lowDetailResolution: {
        x: number;
        y: number;
    };
    
    veryLowDetailResolution: {
        x: number;
        y: number;
    };
    
    highDetailIterations: number;
    
    mediumDetailIterations: number;
    
    lowDetailIterations: number;
    
    veryLowDetailIterations: number;
}

export interface SoftBodyLODOptions {
    
    enabled?: boolean;
    
    config?: Partial<LODConfig>;
    
    cameraEntity?: Entity;
}

export interface SpatialPartitioningOptions {
    
    cellSize?: number;
    
    worldMin?: THREE.Vector3;
    
    worldMax?: THREE.Vector3;
}

export interface SoftBodyComponentOptions {
    
    type: SoftBodyType;
    
    mass?: number;
    
    stiffness?: number;
    
    damping?: number;
    
    fixedCorners?: boolean;
    
    fixedEnds?: boolean;
    
    mesh?: THREE.Mesh;
    
    materialName?: string;
    
    params?: any;
}

export interface SoftBodySystemOptions {
    
    debug?: boolean;
    
    iterations?: number;
    
    physicsSystem?: PhysicsSystem;
    
    useSpatialPartitioning?: boolean;
    
    useLOD?: boolean;
    
    useSoftRigidInteraction?: boolean;
    
    useSoftBodyCutter?: boolean;
}

export interface VesselConnection {
    
    id: string;
    
    type: VesselConnectionType;
    
    position: THREE.Vector3;
    
    direction: THREE.Vector3;
    
    diameter: number;
    
    flowRate: number;
    
    pressure: number;
}

export interface OrganSoftBodyOptions extends SoftBodyComponentOptions {
    
    organType: OrganType;
    
    tissueElasticity?: number;
    
    tissueDensity?: number;
    
    vesselConnections?: VesselConnection[];
}

export interface FluidSystem {
    
    calculateFlow(vessel: VesselComponent, deltaTime: number): number;
    
    updatePressure(vessel: VesselComponent, pressure: number): void;
    
    getViscosity(): number;
    
    getDensity(): number;
}

export interface VascularSystemConfig {
    
    enableFlowSimulation?: boolean;
    
    enablePressureCalculation?: boolean;
    
    enableVesselElasticity?: boolean;
    
    fluidSystem?: FluidSystem;
    
    heartRate?: number;
    
    systolicPressure?: number;
    
    diastolicPressure?: number;
}

export interface BodyTypeConfig {
    
    type: BodyType;
    
    mass?: number;
    
    useGravity?: boolean;
    
    isTrigger?: boolean;
    
    linearDamping?: number;
    
    angularDamping?: number;
}

export interface ColliderConfig {
    
    type: ColliderType;
    
    size?: {
        x?: number;
        y?: number;
        z?: number;
        radius?: number;
        height?: number;
    };
    
    material?: {
        friction?: number;
        restitution?: number;
        density?: number;
    };
    
    isTrigger?: boolean;
    
    layer?: number;
    
    mask?: number;
}

export interface FountainConfig {
    
    width?: number;
    
    height?: number;
    
    depth?: number;
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    color?: THREE.Color;
    
    opacity?: number;
    
    flowSpeed?: number;
    
    flowDirection?: THREE.Vector3;
    
    turbulenceStrength?: number;
    
    turbulenceFrequency?: number;
    
    turbulenceSpeed?: number;
    
    enableMistEffect?: boolean;
    
    mistEffectStrength?: number;
    
    enableSplashEffect?: boolean;
    
    splashEffectStrength?: number;
    
    enableDropletEffect?: boolean;
    
    dropletEffectStrength?: number;
    
    enableSoundEffect?: boolean;
    
    soundEffectVolume?: number;
    
    enableFluidDynamics?: boolean;
    
    fountainType?: FountainType;
    
    fountainMode?: FountainMode;
    
    jetHeight?: number;
    
    jetAngle?: number;
    
    jetCount?: number;
    
    jetInterval?: number;
    
    jetDuration?: number;
    
    jetDelay?: number;
    
    jetRandomness?: number;
    
    enabled?: boolean;
}

export interface EnhancedFountainConfig extends FountainConfig {
    
    jetShape?: FountainJetShape;
    
    jetHeightChangeSpeed?: number;
    
    jetWidthChangeSpeed?: number;
    
    jetAngleChangeSpeed?: number;
    
    jetDensity?: number;
    
    jetParticleSize?: number;
    
    jetParticleSizeVariation?: number;
    
    jetParticleColor?: THREE.Color;
    
    jetParticleColorVariation?: number;
    
    jetParticleLifetime?: number;
    
    jetParticleLifetimeVariation?: number;
    
    jetParticleSpeed?: number;
    
    jetParticleSpeedVariation?: number;
    
    jetParticleGravityFactor?: number;
    
    jetParticleDragFactor?: number;
    
    jetParticleRotationSpeed?: number;
    
    jetParticleRotationSpeedVariation?: number;
    
    enableJetParticleTrails?: boolean;
    
    jetParticleTrailLength?: number;
    
    enableJetParticleScattering?: boolean;
    
    jetParticleScatteringStrength?: number;
    
    enableJetParticleReflections?: boolean;
    
    jetParticleReflectionStrength?: number;
    
    enableJetParticleRefractions?: boolean;
    
    jetParticleRefractionStrength?: number;
    
    enableJetParticleSounds?: boolean;
    
    jetParticleSoundVolume?: number;
    
    enableJetParticleOptimization?: boolean;
    
    jetParticleLODDistances?: number[];
    
    enableGPUAcceleration?: boolean;
    
    enableColoredLighting?: boolean;
    
    coloredLightingColors?: THREE.Color[];
    
    coloredLightingChangeSpeed?: number;
    
    enableMusicSync?: boolean;
    
    musicSyncSensitivity?: number;
    
    musicSyncFrequencyRange?: [number, number];
    
    enableInteractiveControl?: boolean;
    
    interactiveControlSensitivity?: number;
}

export interface EnhancedFountainPresetConfig {
    
    type: EnhancedFountainType;
    
    position?: THREE.Vector3;
    
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    
    color?: THREE.Color;
    
    opacity?: number;
    
    jetShape?: FountainJetShape;
    
    enableColoredLighting?: boolean;
    
    coloredLightingColors?: THREE.Color[];
    
    enableMusicSync?: boolean;
    
    enableInteractiveControl?: boolean;
    
    enableGPUAcceleration?: boolean;
}

export interface RainWaterConfig {
    
    width?: number;
    
    height?: number;
    
    depth?: number;
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    color?: THREE.Color;
    
    opacity?: number;
    
    flowSpeed?: number;
    
    rainWaterType?: RainWaterType;
    
    rainIntensity?: number;
    
    raindropSize?: number;
    
    raindropFrequency?: number;
    
    raindropLifetime?: number;
    
    enableSplashEffect?: boolean;
    
    splashEffectStrength?: number;
    
    enableRippleEffect?: boolean;
    
    rippleEffectStrength?: number;
    
    enableFlowEffect?: boolean;
    
    flowEffectStrength?: number;
    
    enableSoundEffect?: boolean;
    
    soundEffectVolume?: number;
    
    enableFluidDynamics?: boolean;
    
    enabled?: boolean;
}

export interface EnhancedRainWaterConfig extends RainWaterConfig {
    
    raindropShape?: RaindropShape;
    
    raindropDeformationFactor?: number;
    
    raindropCollisionPrecision?: number;
    
    enableWindEffect?: boolean;
    
    windStrength?: number;
    
    windDirection?: THREE.Vector2;
    
    enableRaindropTrails?: boolean;
    
    raindropTrailLength?: number;
    
    enableRaindropRipples?: boolean;
    
    raindropRippleStrength?: number;
    
    enableRaindropPuddles?: boolean;
    
    raindropPuddleMaxDepth?: number;
    
    enableRaindropReflections?: boolean;
    
    raindropReflectionStrength?: number;
    
    enableRaindropRefractions?: boolean;
    
    raindropRefractionStrength?: number;
    
    enableRaindropScattering?: boolean;
    
    raindropScatteringStrength?: number;
    
    enableRaindropSounds?: boolean;
    
    raindropSoundVolume?: number;
    
    enableRaindropOptimization?: boolean;
    
    raindropLODDistances?: number[];
    
    enableGPUAcceleration?: boolean;
}

export interface EnhancedRainWaterPresetConfig {
    
    type: EnhancedRainWaterType;
    
    position?: THREE.Vector3;
    
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    
    color?: THREE.Color;
    
    opacity?: number;
    
    enableWindEffect?: boolean;
    
    windStrength?: number;
    
    windDirection?: THREE.Vector2;
    
    enableRaindropTrails?: boolean;
    
    enableRaindropRipples?: boolean;
    
    enableRaindropPuddles?: boolean;
    
    enableRaindropSounds?: boolean;
    
    enableGPUAcceleration?: boolean;
}

export interface FountainPresetConfig {
    
    type: FountainPresetType;
    
    width?: number;
    
    height?: number;
    
    depth?: number;
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    color?: THREE.Color;
    
    opacity?: number;
    
    flowSpeed?: number;
    
    turbulenceStrength?: number;
    
    turbulenceFrequency?: number;
    
    turbulenceSpeed?: number;
    
    enableMistEffect?: boolean;
    
    mistEffectStrength?: number;
    
    enableSplashEffect?: boolean;
    
    splashEffectStrength?: number;
    
    enableDropletEffect?: boolean;
    
    dropletEffectStrength?: number;
    
    enableSoundEffect?: boolean;
    
    soundEffectVolume?: number;
    
    enableFluidDynamics?: boolean;
    
    jetHeight?: number;
    
    jetAngle?: number;
    
    jetCount?: number;
    
    jetInterval?: number;
    
    jetDuration?: number;
    
    jetDelay?: number;
    
    jetRandomness?: number;
}

export interface HotSpringConfig {
    
    hotSpringType?: HotSpringType;
    
    width?: number;
    
    height?: number;
    
    depth?: number;
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    color?: THREE.Color;
    
    opacity?: number;
    
    temperature?: number;
    
    waveAmplitude?: number;
    
    waveFrequency?: number;
    
    waveSpeed?: number;
    
    enableBubbleEffect?: boolean;
    
    bubbleEffectStrength?: number;
    
    bubbleSizeRange?: [number, number];
    
    bubbleSpeedRange?: [number, number];
    
    bubbleDensity?: number;
    
    bubbleDistributionRadius?: number;
    
    enableBubbleBurstEffect?: boolean;
    
    bubbleBurstEffectStrength?: number;
    
    enableSteamEffect?: boolean;
    
    steamEffectStrength?: number;
    
    steamColor?: THREE.Color;
    
    steamDensity?: number;
    
    steamSizeRange?: [number, number];
    
    steamSpeedRange?: [number, number];
    
    steamRiseHeight?: number;
    
    enableSoundEffect?: boolean;
    
    soundEffectVolume?: number;
    
    enableHeatDiffusion?: boolean;
    
    heatDiffusionRange?: number;
    
    enableMineralEffect?: boolean;
    
    mineralColor?: THREE.Color;
}

export interface HotSpringPresetConfig {
    
    type: HotSpringType;
    
    width?: number;
    
    height?: number;
    
    depth?: number;
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    color?: THREE.Color;
    
    opacity?: number;
    
    temperature?: number;
    
    waveAmplitude?: number;
    
    waveFrequency?: number;
    
    waveSpeed?: number;
    
    enableBubbleEffect?: boolean;
    
    bubbleEffectStrength?: number;
    
    bubbleSizeRange?: [number, number];
    
    bubbleSpeedRange?: [number, number];
    
    bubbleDensity?: number;
    
    bubbleDistributionRadius?: number;
    
    enableBubbleBurstEffect?: boolean;
    
    bubbleBurstEffectStrength?: number;
    
    enableSteamEffect?: boolean;
    
    steamEffectStrength?: number;
    
    steamColor?: THREE.Color;
    
    steamDensity?: number;
    
    steamSizeRange?: [number, number];
    
    steamSpeedRange?: [number, number];
    
    steamRiseHeight?: number;
    
    enableSoundEffect?: boolean;
    
    soundEffectVolume?: number;
    
    enableHeatDiffusion?: boolean;
    
    heatDiffusionRange?: number;
    
    enableMineralEffect?: boolean;
    
    mineralColor?: THREE.Color;
}

export interface LakeGeneratorConfig {
    
    position: THREE.Vector3;
    
    size: {
        width: number;
        depth: number;
    };
    
    depth: number;
    
    shapeType?: LakeShapeType;
    
    shapeParams?: {
        
        irregularity?: number;
        
        seed?: number;
        
        controlPoints?: number;
    };
    
    customShapePoints?: THREE.Vector2[];
    
    resolution?: number;
    
    followTerrain?: boolean;
    
    terrainOffset?: number;
    
    generateShore?: boolean;
    
    shoreWidth?: number;
    
    shoreHeight?: number;
    
    generateLakeBed?: boolean;
    
    lakeBedMaterial?: any;
    
    generateUnderwaterVegetation?: boolean;
    
    underwaterVegetationDensity?: number;
    
    generateUnderwaterParticles?: boolean;
    
    underwaterParticleCount?: number;
}

export interface WaterFlowDirection {
    
    x: number;
    
    y: number;
    
    z: number;
}

export interface WaterWaveParams {
    
    amplitude: number;
    
    frequency: number;
    
    speed: number;
    
    direction: {
        x: number;
        z: number;
    };
}

export interface WaterBodyConfig {
    
    type?: WaterBodyType;
    
    shape?: WaterBodyShape;
    
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    
    density?: number;
    
    viscosity?: number;
    
    temperature?: number;
    
    flowDirection?: WaterFlowDirection;
    
    flowSpeed?: number;
    
    waveParams?: WaterWaveParams;
    
    enableBuoyancy?: boolean;
    
    enableDrag?: boolean;
    
    enableWaves?: boolean;
    
    enableFlow?: boolean;
    
    enableReflection?: boolean;
    
    enableRefraction?: boolean;
    
    enableCaustics?: boolean;
    
    enableUnderwaterFog?: boolean;
    
    enableUnderwaterDistortion?: boolean;
    
    enableParticles?: boolean;
    
    enabled?: boolean;
}

export interface LakeWaterConfig extends WaterBodyConfig {
    
    shapeType?: LakeShapeType;
    
    width?: number;
    
    length?: number;
    
    depth?: number;
    
    edgePoints?: THREE.Vector2[];
    
    resolution?: number;
    
    bedHeightVariation?: number;
    
    generateShore?: boolean;
    
    shoreHeight?: number;
    
    shoreWidth?: number;
}

export interface OceanFFTWaveSimulationConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    resolution?: number;
    
    size?: number;
    
    windSpeed?: number;
    
    windDirection?: number;
    
    waveScale?: number;
    
    waveHeight?: number;
    
    waveSpeed?: number;
    
    useGPUCompute?: boolean;
    
    useDebugVisualization?: boolean;
}

export interface OceanGeneratorConfig {
    
    position: THREE.Vector3;
    
    size: {
        width: number;
        depth: number;
    };
    
    depth: number;
    
    resolution?: number;
    
    followTerrain?: boolean;
    
    terrainOffset?: number;
    
    generateSeabed?: boolean;
    
    seabedMaterial?: any;
    
    generateUnderwaterVegetation?: boolean;
    
    underwaterVegetationDensity?: number;
    
    generateUnderwaterParticles?: boolean;
    
    underwaterParticleCount?: number;
    
    generateWaves?: boolean;
    
    waveHeight?: number;
    
    waveFrequency?: number;
    
    waveSpeed?: number;
    
    waveDirection?: THREE.Vector2;
    
    generateCoastline?: boolean;
    
    coastlineWidth?: number;
    
    coastlineHeight?: number;
    
    generateFoam?: boolean;
    
    foamDensity?: number;
    
    generateReflection?: boolean;
    
    reflectionStrength?: number;
    
    generateRefraction?: boolean;
    
    refractionStrength?: number;
    
    generateCaustics?: boolean;
    
    causticsStrength?: number;
}

export interface OceanWaterConfig extends WaterBodyConfig {
    
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    
    depth?: number;
    
    resolution?: {
        width: number;
        depth: number;
    };
    
    waveType?: OceanWaveType;
    
    waveHeight?: number;
    
    waveFrequency?: number;
    
    waveDirection?: {
        x: number;
        z: number;
    };
    
    useFFTWaves?: boolean;
    
    enableTides?: boolean;
    
    tidePeriod?: number;
    
    tideHeight?: number;
    
    enableSeabed?: boolean;
    
    seabedHeightVariation?: number;
}

export interface RainWaterPresetConfig {
    
    type: RainWaterPresetType;
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    
    enabled?: boolean;
}

export interface RiverPathPoint {
    
    position: THREE.Vector3;
    
    width: number;
    
    depth: number;
    
    flowSpeed?: number;
}

export interface RiverGeneratorConfig {
    
    pathPoints: RiverPathPoint[];
    
    segments?: number;
    
    followTerrain?: boolean;
    
    terrainOffset?: number;
    
    useSpline?: boolean;
    
    generateRiverBanks?: boolean;
    
    riverBankHeight?: number;
    
    riverBankWidth?: number;
    
    generateRiverBed?: boolean;
    
    riverBedMaterial?: any;
    
    generateFlowParticles?: boolean;
    
    flowParticleCount?: number;
}

export interface RiverWaterConfig extends WaterBodyConfig {
    
    pathPoints?: THREE.Vector3[];
    
    width?: number;
    
    depth?: number;
    
    flowSpeed?: number;
    
    curvature?: number;
    
    bedHeightVariation?: number;
    
    bankHeight?: number;
    
    generateBanks?: boolean;
    
    useSpline?: boolean;
}

export interface WaterfallConfig {
    
    type?: WaterfallType;
    
    width?: number;
    
    height?: number;
    
    depth?: number;
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    color?: THREE.Color;
    
    opacity?: number;
    
    flowSpeed?: number;
    
    flowDirection?: {
        x: number;
        y: number;
        z: number;
    };
    
    turbulenceStrength?: number;
    
    turbulenceFrequency?: number;
    
    turbulenceSpeed?: number;
}

export interface WaterfallPresetConfig {
    
    type: WaterfallPresetType;
    
    width?: number;
    
    height?: number;
    
    depth?: number;
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    color?: THREE.Color;
    
    opacity?: number;
    
    flowSpeed?: number;
    
    flowDirection?: {
        x: number;
        y: number;
        z: number;
    };
    
    turbulenceStrength?: number;
    
    turbulenceFrequency?: number;
    
    turbulenceSpeed?: number;
}

export interface WaterGenerationConfig {
    
    preset: WaterPresetType;
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    scale?: THREE.Vector3;
    
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    
    physicsParams?: {
        density?: number;
        viscosity?: number;
        surfaceTension?: number;
    };
    
    waveParams?: {
        amplitude?: number;
        frequency?: number;
        speed?: number;
        direction?: THREE.Vector2;
    };
    
    flowParams?: {
        direction?: THREE.Vector3;
        speed?: number;
    };
    
    visualParams?: {
        color?: THREE.Color;
        opacity?: number;
        reflectivity?: number;
        refractivity?: number;
    };
    
    entityName?: string;
    
    entityId?: string;
}

export interface WaterInteractionSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enableSplashEffect?: boolean;
    
    enableRippleEffect?: boolean;
    
    enableDropletEffect?: boolean;
    
    enableFlowEffect?: boolean;
    
    enableSplittingEffect?: boolean;
    
    enableBuoyancyEffect?: boolean;
    
    enableDragEffect?: boolean;
    
    useVoxelBuoyancy?: boolean;
    
    voxelResolution?: number;
    
    enableBuoyancyStabilization?: boolean;
    
    buoyancyStabilizationStrength?: number;
    
    useDirectionalDrag?: boolean;
    
    enableTurbulenceDrag?: boolean;
    
    enableRotationalDrag?: boolean;
    
    rotationalDragStrength?: number;
    
    rotationalDragCoefficient?: number;
    
    dragCoefficientX?: number;
    
    dragCoefficientY?: number;
    
    dragCoefficientZ?: number;
    
    useAdvancedFlowInteraction?: boolean;
    
    enableTurbulentFlow?: boolean;
    
    flowCoefficientX?: number;
    
    flowCoefficientY?: number;
    
    flowCoefficientZ?: number;
    
    enablePerformanceMonitoring?: boolean;
    
    enableDebugVisualization?: boolean;
    
    splashEffectStrength?: number;
    
    rippleEffectStrength?: number;
    
    dropletEffectStrength?: number;
    
    flowEffectStrength?: number;
    
    splittingEffectStrength?: number;
    
    buoyancyEffectStrength?: number;
    
    dragEffectStrength?: number;
}

export interface WaterInfo {
    id: string;
    name: string;
    type: WaterPresetType;
    entity: Entity;
    waterBody: WaterBodyComponent;
    createdAt: Date;
    lastUpdated: Date;
}

export interface WaterStats {
    totalWaterBodies: number;
    typeDistribution: Record<WaterPresetType, number>;
    totalVolume: number;
    averageDensity: number;
    activeWaterBodies: number;
}

export interface WaterPhysicsOptimizationSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enableDebugVisualization?: boolean;
    
    enablePerformanceMonitoring?: boolean;
    
    enableAutoOptimization?: boolean;
    
    enableBatteryOptimization?: boolean;
    
    enableTemperatureOptimization?: boolean;
    
    targetFPS?: number;
    
    minAcceptableFPS?: number;
    
    defaultPerformanceLevel?: DevicePerformanceLevel;
    
    lowBatteryThreshold?: number;
    
    highTemperatureThreshold?: number;
    
    enableSpatialPartitioning?: boolean;
    
    enableMultithreading?: boolean;
    
    enableAdaptivePhysicsUpdate?: boolean;
    
    enablePhysicsLOD?: boolean;
    
    enablePhysicsSleeping?: boolean;
    
    enablePhysicsCaching?: boolean;
    
    enablePhysicsPrediction?: boolean;
    
    enablePhysicsInterpolation?: boolean;
    
    enablePhysicsSimplification?: boolean;
    
    enablePhysicsInstancing?: boolean;
    
    enablePhysicsBatching?: boolean;
    
    enablePhysicsCulling?: boolean;
    
    enablePhysicsPriority?: boolean;
    
    enablePhysicsDeferredUpdate?: boolean;
    
    enablePhysicsAsyncUpdate?: boolean;
    
    enablePhysicsGPUAcceleration?: boolean;
    
    enablePhysicsSIMDAcceleration?: boolean;
    
    enablePhysicsWebAssemblyAcceleration?: boolean;
    
    enablePhysicsSharedArrayBufferAcceleration?: boolean;
    
    enablePhysicsWebWorkerAcceleration?: boolean;
    
    enablePhysicsWebGLAcceleration?: boolean;
    
    enablePhysicsWebCLAcceleration?: boolean;
    
    enablePhysicsWebNNAcceleration?: boolean;
    
    enablePhysicsWebMLAcceleration?: boolean;
    
    enablePhysicsWebXRAcceleration?: boolean;
    
    enablePhysicsWebVRAcceleration?: boolean;
    
    enablePhysicsWebARAcceleration?: boolean;
    
    enablePhysicsWebAudioAcceleration?: boolean;
    
    enablePhysicsWebRTCAcceleration?: boolean;
    
    enablePhysicsWebSocketsAcceleration?: boolean;
    
    enablePhysicsWebTransportAcceleration?: boolean;
    
    enablePhysicsWebCodecsAcceleration?: boolean;
    
    enablePhysicsWebUSBAcceleration?: boolean;
    
    enablePhysicsWebSerialAcceleration?: boolean;
    
    enablePhysicsWebHIDAcceleration?: boolean;
    
    enablePhysicsWebNFCAcceleration?: boolean;
    
    enablePhysicsWebMIDIAcceleration?: boolean;
    
    enablePhysicsWebShareAcceleration?: boolean;
    
    enablePhysicsWebPaymentAcceleration?: boolean;
    
    enablePhysicsWebCredentialAcceleration?: boolean;
    
    enablePhysicsWebAuthenticationAcceleration?: boolean;
    
    enablePhysicsWebCryptoAcceleration?: boolean;
    
    enablePhysicsWebSQLAcceleration?: boolean;
    
    enablePhysicsWebIndexedDBAcceleration?: boolean;
    
    enablePhysicsWebServiceWorkerAcceleration?: boolean;
    
    enablePhysicsWebPushAcceleration?: boolean;
    
    enablePhysicsWebNotificationAcceleration?: boolean;
    
    enablePhysicsWebBackgroundAcceleration?: boolean;
    
    enablePhysicsWebSyncAcceleration?: boolean;
    
    enablePhysicsWebPeriodicSyncAcceleration?: boolean;
    
    enablePhysicsWebBudgetAcceleration?: boolean;
    
    enablePhysicsWebPermissionAcceleration?: boolean;
    
    enablePhysicsWebLockAcceleration?: boolean;
    
    enablePhysicsWebWakeLockAcceleration?: boolean;
    
    enablePhysicsWebVibrationAcceleration?: boolean;
    
    enablePhysicsWebGeolocationAcceleration?: boolean;
    
    enablePhysicsWebDeviceOrientationAcceleration?: boolean;
    
    enablePhysicsWebDeviceMotionAcceleration?: boolean;
    
    enablePhysicsWebAmbientLightAcceleration?: boolean;
    
    enablePhysicsWebProximityAcceleration?: boolean;
    
    enablePhysicsWebBatteryAcceleration?: boolean;
    
    enablePhysicsWebNetworkAcceleration?: boolean;
    
    enablePhysicsWebCPUAcceleration?: boolean;
    
    enablePhysicsWebHardwareAcceleration?: boolean;
    
    enablePhysicsWebSoftwareAcceleration?: boolean;
    
    enablePhysicsWebOSAcceleration?: boolean;
    
    enablePhysicsWebBrowserAcceleration?: boolean;
    
    enablePhysicsWebEngineAcceleration?: boolean;
    
    enablePhysicsWebRuntimeAcceleration?: boolean;
    
    enablePhysicsWebVMAcceleration?: boolean;
    
    enablePhysicsWebJITAcceleration?: boolean;
    
    enablePhysicsWebAOTAcceleration?: boolean;
    
    enablePhysicsWebInterpreterAcceleration?: boolean;
    
    enablePhysicsWebCompilerAcceleration?: boolean;
    
    enablePhysicsWebOptimizerAcceleration?: boolean;
    
    enablePhysicsWebDebuggerAcceleration?: boolean;
    
    enablePhysicsWebProfilerAcceleration?: boolean;
    
    enablePhysicsWebAnalyzerAcceleration?: boolean;
    
    enablePhysicsWebMonitorAcceleration?: boolean;
    
    enablePhysicsWebLoggerAcceleration?: boolean;
    
    enablePhysicsWebTracerAcceleration?: boolean;
    
    enablePhysicsWebInspectorAcceleration?: boolean;
    
    enablePhysicsWebConsoleAcceleration?: boolean;
    
    enablePhysicsWebTerminalAcceleration?: boolean;
    
    enablePhysicsWebShellAcceleration?: boolean;
    
    enablePhysicsWebCommandAcceleration?: boolean;
    
    enablePhysicsWebScriptAcceleration?: boolean;
    
    enablePhysicsWebModuleAcceleration?: boolean;
    
    enablePhysicsWebPackageAcceleration?: boolean;
    
    enablePhysicsWebLibraryAcceleration?: boolean;
    
    enablePhysicsWebFrameworkAcceleration?: boolean;
    
    enablePhysicsWebPlatformAcceleration?: boolean;
    
    enablePhysicsWebAPIAcceleration?: boolean;
    
    enablePhysicsWebSDKAcceleration?: boolean;
    
    enablePhysicsWebIDEAcceleration?: boolean;
    
    enablePhysicsWebEditorAcceleration?: boolean;
    
    enablePhysicsWebDesignerAcceleration?: boolean;
    
    enablePhysicsWebBuilderAcceleration?: boolean;
    
    enablePhysicsWebTesterAcceleration?: boolean;
    
    enablePhysicsWebDeployerAcceleration?: boolean;
    
    enablePhysicsWebPublisherAcceleration?: boolean;
    
    enablePhysicsWebDistributorAcceleration?: boolean;
    
    enablePhysicsWebInstallerAcceleration?: boolean;
    
    enablePhysicsWebUpdaterAcceleration?: boolean;
    
    enablePhysicsWebLauncherAcceleration?: boolean;
    
    enablePhysicsWebRunnerAcceleration?: boolean;
    
    enablePhysicsWebExecutorAcceleration?: boolean;
    
    enablePhysicsWebSchedulerAcceleration?: boolean;
    
    enablePhysicsWebTimerAcceleration?: boolean;
    
    enablePhysicsWebClockAcceleration?: boolean;
    
    enablePhysicsWebCalendarAcceleration?: boolean;
    
    enablePhysicsWebDateAcceleration?: boolean;
    
    enablePhysicsWebTimeAcceleration?: boolean;
    
    enablePhysicsWebDurationAcceleration?: boolean;
    
    enablePhysicsWebIntervalAcceleration?: boolean;
    
    enablePhysicsWebTimeoutAcceleration?: boolean;
    
    enablePhysicsWebDelayAcceleration?: boolean;
    
    enablePhysicsWebSleepAcceleration?: boolean;
    
    enablePhysicsWebWaitAcceleration?: boolean;
    
    enablePhysicsWebAsyncAcceleration?: boolean;
    
    enablePhysicsWebPromiseAcceleration?: boolean;
    
    enablePhysicsWebFutureAcceleration?: boolean;
    
    enablePhysicsWebTaskAcceleration?: boolean;
    
    enablePhysicsWebJobAcceleration?: boolean;
    
    enablePhysicsWebWorkAcceleration?: boolean;
    
    enablePhysicsWebProcessAcceleration?: boolean;
    
    enablePhysicsWebThreadAcceleration?: boolean;
    
    enablePhysicsWebFiberAcceleration?: boolean;
    
    enablePhysicsWebCoroutineAcceleration?: boolean;
    
    enablePhysicsWebGeneratorAcceleration?: boolean;
    
    enablePhysicsWebIteratorAcceleration?: boolean;
    
    enablePhysicsWebStreamAcceleration?: boolean;
    
    enablePhysicsWebPipeAcceleration?: boolean;
    
    enablePhysicsWebChannelAcceleration?: boolean;
    
    enablePhysicsWebQueueAcceleration?: boolean;
    
    enablePhysicsWebStackAcceleration?: boolean;
    
    enablePhysicsWebHeapAcceleration?: boolean;
    
    enablePhysicsWebListAcceleration?: boolean;
    
    enablePhysicsWebArrayAcceleration?: boolean;
    
    enablePhysicsWebVectorAcceleration?: boolean;
    
    enablePhysicsWebMatrixAcceleration?: boolean;
    
    enablePhysicsWebTensorAcceleration?: boolean;
    
    enablePhysicsWebGraphAcceleration?: boolean;
    
    enablePhysicsWebTreeAcceleration?: boolean;
    
    enablePhysicsWebMapAcceleration?: boolean;
    
    enablePhysicsWebSetAcceleration?: boolean;
    
    enablePhysicsWebDictionaryAcceleration?: boolean;
    
    enablePhysicsWebTableAcceleration?: boolean;
    
    enablePhysicsWebDatabaseAcceleration?: boolean;
    
    enablePhysicsWebDiskAcceleration?: boolean;
    
    enablePhysicsWebFileAcceleration?: boolean;
    
    enablePhysicsWebDirectoryAcceleration?: boolean;
    
    enablePhysicsWebPathAcceleration?: boolean;
    
    enablePhysicsWebURLAcceleration?: boolean;
    
    enablePhysicsWebURIAcceleration?: boolean;
    
    enablePhysicsWebHTTPAcceleration?: boolean;
    
    enablePhysicsWebHTTPSAcceleration?: boolean;
    
    enablePhysicsWebFTPAcceleration?: boolean;
    
    enablePhysicsWebSFTPAcceleration?: boolean;
    
    enablePhysicsWebSSHAcceleration?: boolean;
    
    enablePhysicsWebTelnetAcceleration?: boolean;
    
    enablePhysicsWebRDPAcceleration?: boolean;
    
    enablePhysicsWebVNCAcceleration?: boolean;
    
    enablePhysicsWebSMBAcceleration?: boolean;
    
    enablePhysicsWebNFSAcceleration?: boolean;
    
    enablePhysicsWebCIFSAcceleration?: boolean;
    
    enablePhysicsWebWebDAVAcceleration?: boolean;
    
    enablePhysicsWebCalDAVAcceleration?: boolean;
    
    enablePhysicsWebCardDAVAcceleration?: boolean;
    
    enablePhysicsWebIMAPAcceleration?: boolean;
    
    enablePhysicsWebPOP3Acceleration?: boolean;
    
    enablePhysicsWebSMTPAcceleration?: boolean;
    
    enablePhysicsWebDNSAcceleration?: boolean;
    
    enablePhysicsWebDHCPAcceleration?: boolean;
    
    enablePhysicsWebNTPAcceleration?: boolean;
    
    enablePhysicsWebSNMPAcceleration?: boolean;
    
    enablePhysicsWebSNTPAcceleration?: boolean;
    
    enablePhysicsWebSyslogAcceleration?: boolean;
    
    enablePhysicsWebTFTPAcceleration?: boolean;
    
    enablePhysicsWebUDPAcceleration?: boolean;
    
    enablePhysicsWebTCPAcceleration?: boolean;
    
    enablePhysicsWebICMPAcceleration?: boolean;
    
    enablePhysicsWebIPAcceleration?: boolean;
    
    enablePhysicsWebIPv4Acceleration?: boolean;
    
    enablePhysicsWebIPv6Acceleration?: boolean;
    
    enablePhysicsWebARPAcceleration?: boolean;
    
    enablePhysicsWebRARPAcceleration?: boolean;
    
    enablePhysicsWebMACAcceleration?: boolean;
    
    enablePhysicsWebEthernetAcceleration?: boolean;
    
    enablePhysicsWebWiFiAcceleration?: boolean;
    
    enablePhysicsWebBluetoothAcceleration?: boolean;
    
    enablePhysicsWebZigBeeAcceleration?: boolean;
    
    enablePhysicsWebZ_WaveAcceleration?: boolean;
    
    enablePhysicsWebLoRaAcceleration?: boolean;
    
    enablePhysicsWebSigFoxAcceleration?: boolean;
    
    enablePhysicsWebNB_IoTAcceleration?: boolean;
    
    enablePhysicsWebLTE_MAcceleration?: boolean;
    
    enablePhysicsWeb5GAcceleration?: boolean;
    
    enablePhysicsWeb4GAcceleration?: boolean;
    
    enablePhysicsWeb3GAcceleration?: boolean;
    
    enablePhysicsWeb2GAcceleration?: boolean;
    
    enablePhysicsWeb1GAcceleration?: boolean;
    
    enablePhysicsWebGSMAcceleration?: boolean;
    
    enablePhysicsWebCDMAAcceleration?: boolean;
    
    enablePhysicsWebTDMAAcceleration?: boolean;
    
    enablePhysicsWebFDMAAcceleration?: boolean;
    
    enablePhysicsWebOFDMAAcceleration?: boolean;
    
    enablePhysicsWebSCFDMAAcceleration?: boolean;
    
    enablePhysicsWebCDMA2000Acceleration?: boolean;
    
    enablePhysicsWebWCDMAAcceleration?: boolean;
    
    enablePhysicsWebHSDPAAcceleration?: boolean;
    
    enablePhysicsWebHSUPAAcceleration?: boolean;
    
    enablePhysicsWebHSPAAcceleration?: boolean;
    
    enablePhysicsWebHSPAplusAcceleration?: boolean;
    
    enablePhysicsWebDC_HSDPAAcceleration?: boolean;
    
    enablePhysicsWebDC_HSPAAcceleration?: boolean;
    
    enablePhysicsWebDC_HSPAplusAcceleration?: boolean;
    
    enablePhysicsWebLTEAcceleration?: boolean;
    
    enablePhysicsWebLTE_AAcceleration?: boolean;
    
    enablePhysicsWebLTE_BAcceleration?: boolean;
    
    enablePhysicsWebLTE_UAcceleration?: boolean;
    
    enablePhysicsWebLTE_LAA?: boolean;
}

export interface WaterPhysicsSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enableBuoyancy?: boolean;
    
    enableDrag?: boolean;
    
    enableFlow?: boolean;
    
    enableWaves?: boolean;
    
    enableCollision?: boolean;
    
    enableParticles?: boolean;
    
    enableDebugVisualization?: boolean;
    
    enablePerformanceMonitoring?: boolean;
    
    enableSpatialPartitioning?: boolean;
    
    spatialGridSize?: number;
    
    enableAdaptiveUpdate?: boolean;
    
    minUpdateFrequency?: number;
    
    maxUpdateFrequency?: number;
    
    enableMultithreading?: boolean;
    
    workerCount?: number;
    
    enableFlowImpact?: boolean;
    
    enableWaterSplitting?: boolean;
}

export interface WaterWavesUpdateParams {
    
    heightMap: Float32Array;
    
    resolution: number;
    
    waveParams: {
        
        amplitude: number;
        
        frequency: number;
        
        speed: number;
        
        direction: {
            x: number;
            z: number;
        };
    };
    
    time: number;
    
    deltaTime: number;
}

export interface WaterFlowUpdateParams {
    
    heightMap: Float32Array;
    
    velocityMap: Float32Array;
    
    resolution: number;
    
    flowDirection: {
        x: number;
        y: number;
        z: number;
    };
    
    flowSpeed: number;
    
    deltaTime: number;
}

export interface NormalMapUpdateParams {
    
    heightMap: Float32Array;
    
    normalMap: Float32Array;
    
    resolution: number;
}

export interface WaterPresetConfig {
    
    type: WaterPresetType;
    
    size?: {
        width: number;
        height: number;
        depth: number;
    };
    
    position?: THREE.Vector3;
    
    rotation?: THREE.Euler;
    
    color?: THREE.Color;
    
    opacity?: number;
    
    reflectivity?: number;
    
    refractivity?: number;
    
    waveParams?: {
        amplitude: number;
        frequency: number;
        speed: number;
        direction: THREE.Vector2;
    };
    
    flowParams?: {
        direction: THREE.Vector3;
        speed: number;
    };
    
    physicsParams?: {
        density: number;
        viscosity: number;
        surfaceTension?: number;
    };
}

export interface WaterWeatherSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    weatherType?: WeatherType;
    
    windSpeed?: number;
    
    windDirection?: number;
    
    rainIntensity?: number;
    
    snowIntensity?: number;
    
    fogIntensity?: number;
    
    thunderIntensity?: number;
    
    useDebugVisualization?: boolean;
}

export interface IntentRecognitionResult {
    intent: IntentType;
    confidence: number;
    entities: any[];
    parameters: Record<string, any>;
}

export interface DigitalHumanAction {
    type: ActionType;
    parameters: Record<string, any>;
    duration: number;
    delay: number;
    priority: number;
}

export interface ActionMappingResult {
    actions: DigitalHumanAction[];
    priority: number;
    duration: number;
}

export interface ConversationTurn {
    id: string;
    userQuery: string;
    systemResponse: string;
    timestamp: Date;
    context?: any;
}

export interface UserProfile {
    id: string;
    preferences: string[];
    interests: string[];
    language: string;
    expertiseLevel: 'beginner' | 'intermediate' | 'advanced';
    visitHistory: string[];
}

export interface ContextInfo {
    conversationHistory: ConversationTurn[];
    currentLocation?: string;
    userProfile?: UserProfile;
    sessionId: string;
    timestamp: Date;
}

export interface QueryAnalysis {
    type: QueryType;
    intent: string;
    entities: Entity[];
    keywords: string[];
    complexity: number;
    language: string;
    requiresContext: boolean;
}

export interface RetrievalResult {
    searchResults: SearchResult[];
    context: ContextInfo;
    queryAnalysis: QueryAnalysis;
    retrievalMetadata: {
        strategy: RetrievalStrategy;
        totalResults: number;
        processingTime: number;
        confidence: number;
    };
}

export interface DialogueResponse {
    text: string;
    intent: IntentRecognitionResult;
    emotion: EmotionAnalysisResult;
    actions: ActionMappingResult;
    sources: any[];
    confidence: number;
    followUpQuestions: string[];
    metadata: {
        processingTime: number;
        retrievalResults: RetrievalResult;
    };
}

export interface DialogueState {
    sessionId: string;
    currentTopic: string;
    userProfile: any;
    conversationHistory: ConversationTurn[];
    context: ContextInfo;
    lastIntent: IntentType;
    lastEmotion: EmotionType;
}

export interface DigitalHumanAppearance {
    model: string;
    textures: Record<string, string>;
    materials: Record<string, any>;
    scale: THREE.Vector3;
    animations: Record<string, AnimationState>;
}

export interface InteractionConfig {
    enableVoiceInput: boolean;
    enableVoiceOutput: boolean;
    enableGestures: boolean;
    enableFacialExpressions: boolean;
    enableLipSync: boolean;
    responseDelay: number;
    idleTimeout: number;
    maxConversationLength: number;
    autoNavigation: boolean;
}

export interface DocumentMetadata {
    title: string;
    category: string;
    tags: string[];
    author?: string;
    createdAt: Date;
    language: string;
    description?: string;
    source?: string;
    version?: string;
}

export interface DocumentChunk {
    id: string;
    content: string;
    index: number;
    startOffset: number;
    endOffset: number;
    metadata?: Record<string, any>;
    embedding?: number[];
}

export interface Document {
    id: string;
    filename: string;
    content: string;
    metadata: DocumentMetadata;
    chunks: DocumentChunk[];
    createdAt: Date;
    updatedAt: Date;
    size: number;
    type: string;
}

export interface ChunkOptions {
    chunkSize: number;
    overlap: number;
    preserveSentences: boolean;
    preserveParagraphs: boolean;
}

export interface SearchOptions {
    topK?: number;
    threshold?: number;
    category?: string;
    tags?: string[];
    language?: string;
}

export interface KnowledgeBaseConfig {
    chunkSize: number;
    chunkOverlap: number;
    embeddingModel: string;
    vectorStoreConfig: any;
    embeddingConfig: any;
    maxDocumentSize: number;
    supportedFileTypes: string[];
}

export interface VectorStore {
    addDocuments(chunks: DocumentChunk[], embeddings: number[][], metadata: any): Promise<void>;
    search(queryEmbedding: number[], options: any): Promise<SearchResult[]>;
    deleteDocument(documentId: string): Promise<void>;
    updateDocument(documentId: string, chunks: DocumentChunk[], embeddings: number[][]): Promise<void>;
    getStats(): Promise<any>;
}

export interface EmbeddingModel {
    embed(text: string): Promise<number[]>;
    embedBatch(texts: string[]): Promise<number[][]>;
    getDimension(): number;
    getModelInfo(): any;
}

export interface NavigationConfig {
    moveSpeed: number;
    rotationSpeed: number;
    stopThreshold: number;
    lookAhead: number;
    smoothRotation: boolean;
    autoStart: boolean;
    debug: boolean;
}

export interface NavigationEventData {
    state: NavigationState;
    progress: number;
    currentStopPoint?: StopPoint;
    position: THREE.Vector3;
    direction: THREE.Vector3;
}

export interface IPathPoint {
    id: string;
    position: THREE.Vector3;
    type: PathPointType;
    waitTime?: number;
    actions?: string[];
    knowledgePointId?: string;
    metadata?: Record<string, any>;
}

export interface IStopPoint extends IPathPoint {
    type: 'stop';
    waitTime: number;
    triggerActions: string[];
    knowledgePointId?: string;
    interactionRadius?: number;
}

export interface PathEditorConfig {
    pathType: 'digital_human_navigation';
    allowCurves: boolean;
    showDirection: boolean;
    enableStops: boolean;
    smoothness?: number;
    segments?: number;
    debug?: boolean;
}

export interface RetrievalConfig {
    strategy: RetrievalStrategy;
    maxResults: number;
    semanticWeight: number;
    keywordWeight: number;
    contextWeight: number;
    diversityThreshold: number;
    relevanceThreshold: number;
    useReranking: boolean;
    enableContextualFiltering: boolean;
}

export interface AnswerGenerationResult {
    answer: string;
    confidence: number;
    sources: SearchResult[];
    reasoning: string;
    followUpQuestions: string[];
    metadata: {
        generationTime: number;
        model: string;
        temperature: number;
    };
}

export interface KnowledgeData {
    id: string;
    title: string;
    content: string;
    type: 'text' | 'image' | 'video' | 'audio' | 'document';
    tags: string[];
    category: string;
    priority: number;
    relatedTopics: string[];
    metadata?: Record<string, any>;
}

export interface SpeechRecognitionConfig {
    language: string;
    continuous: boolean;
    interimResults: boolean;
    maxAlternatives: number;
    grammars?: string[];
    noiseReduction: boolean;
    echoCancellation: boolean;
    autoGainControl: boolean;
    sampleRate: number;
    provider: 'web-speech' | 'azure' | 'google' | 'baidu';
    apiKey?: string;
    region?: string;
}

export interface SpeechAlternative {
    transcript: string;
    confidence: number;
}

export interface SpeechRecognitionResult {
    transcript: string;
    confidence: number;
    isFinal: boolean;
    alternatives: SpeechAlternative[];
    timestamp: Date;
    duration: number;
    language: string;
}

export interface SpeechSynthesisConfig {
    language: string;
    voice?: string;
    rate: number;
    pitch: number;
    volume: number;
    provider: 'web-speech' | 'azure' | 'google' | 'baidu' | 'iflytek';
    apiKey?: string;
    region?: string;
    enableEmotionalSynthesis: boolean;
    enableLipSync: boolean;
    audioFormat: 'mp3' | 'wav' | 'ogg';
    sampleRate: number;
}

export interface LipSyncData {
    time: number;
    phoneme: string;
    viseme: string;
    intensity: number;
}

export interface SpeechSynthesisResult {
    audioUrl?: string;
    audioBuffer?: ArrayBuffer;
    duration: number;
    text: string;
    language: string;
    voice: string;
    lipSyncData?: LipSyncData[];
    timestamp: Date;
}

export interface VoiceParameters {
    rate: number;
    pitch: number;
    volume: number;
    emotion?: EmotionType;
    emotionIntensity?: number;
    emphasis?: string[];
    pauses?: {
        position: number;
        duration: number;
    }[];
}

export interface CameraOptions {
    
    type?: CameraType;
    
    fov?: number;
    
    near?: number;
    
    far?: number;
    
    aspect?: number;
    
    left?: number;
    
    right?: number;
    
    top?: number;
    
    bottom?: number;
    
    autoAspect?: boolean;
}

export interface VolumetricEffectsSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enableVolumetricLight?: boolean;
    
    enableVolumetricFog?: boolean;
    
    enableDebugVisualization?: boolean;
    
    enablePerformanceMonitoring?: boolean;
}

export interface VolumetricLightConfig {
    
    position: THREE.Vector3;
    
    direction: THREE.Vector3;
    
    color: THREE.Color;
    
    intensity: number;
    
    decay: number;
    
    length: number;
    
    angle: number;
    
    density: number;
}

export interface VolumetricFogConfig {
    
    position: THREE.Vector3;
    
    size: THREE.Vector3;
    
    color: THREE.Color;
    
    density: number;
    
    enableNoise?: boolean;
    
    noiseScale?: number;
    
    noiseSpeed?: number;
}

export interface LightProbeSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enableDebugVisualization?: boolean;
}

export interface LightProbeGroupConfig {
    
    spacing?: number;
    
    resolution?: {
        x: number;
        y: number;
        z: number;
    };
    
    intensity?: number;
    
    autoUpdate?: boolean;
}

export interface UndergroundLightingSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enableCaveLighting?: boolean;
    
    enableStalactiteReflection?: boolean;
    
    enableWaterReflection?: boolean;
    
    enableVolumetricLight?: boolean;
    
    enableVolumetricFog?: boolean;
    
    enableDebugVisualization?: boolean;
    
    enablePerformanceMonitoring?: boolean;
}

export interface CaveLightingConfig {
    
    position: THREE.Vector3;
    
    size: THREE.Vector3;
    
    intensity: number;
    
    color: THREE.Color;
    
    decay: number;
    
    castShadow: boolean;
}

export interface StalactiteReflectionConfig {
    
    position: THREE.Vector3;
    
    size: number;
    
    intensity: number;
    
    color: THREE.Color;
    
    flickerFrequency: number;
    
    flickerIntensity: number;
}

export interface WaterReflectionConfig {
    
    position: THREE.Vector3;
    
    size: {
        width: number;
        depth: number;
    };
    
    intensity: number;
    
    color: THREE.Color;
    
    waveFrequency: number;
    
    waveIntensity: number;
}

export interface LightOptions {
    
    type: string;
    
    color?: THREE.ColorRepresentation;
    
    intensity?: number;
    
    castShadow?: boolean;
}

export interface AmbientLightOptions extends LightOptions {
    type: 'ambient';
}

export interface DirectionalLightOptions extends LightOptions {
    type: 'directional';
    
    shadowMapSize?: number;
    
    shadowCameraNear?: number;
    
    shadowCameraFar?: number;
    
    shadowCameraLeft?: number;
    
    shadowCameraRight?: number;
    
    shadowCameraTop?: number;
    
    shadowCameraBottom?: number;
    
    shadowBias?: number;
    
    shadowRadius?: number;
}

export interface PointLightOptions extends LightOptions {
    type: 'point';
    
    distance?: number;
    
    decay?: number;
    
    shadowMapSize?: number;
    
    shadowCameraNear?: number;
    
    shadowCameraFar?: number;
    
    shadowBias?: number;
    
    shadowRadius?: number;
}

export interface SpotLightOptions extends LightOptions {
    type: 'spot';
    
    distance?: number;
    
    angle?: number;
    
    penumbra?: number;
    
    decay?: number;
    
    shadowMapSize?: number;
    
    shadowCameraNear?: number;
    
    shadowCameraFar?: number;
    
    shadowBias?: number;
    
    shadowRadius?: number;
}

export interface HemisphereLightOptions extends LightOptions {
    type: 'hemisphere';
    
    groundColor?: THREE.ColorRepresentation;
}

export interface RectAreaLightOptions extends LightOptions {
    type: 'rectArea';
    
    width?: number;
    
    height?: number;
}

export interface LightShaftConfig {
    
    type: LightShaftType;
    
    position: THREE.Vector3;
    
    direction?: THREE.Vector3;
    
    color?: THREE.Color | number | string;
    
    intensity?: number;
    
    decay?: number;
    
    distance?: number;
    
    angle?: number;
    
    penumbra?: number;
    
    density?: number;
    
    scattering?: number;
    
    samples?: number;
    
    castShadow?: boolean;
    
    shadowMapSize?: number;
    
    enableDynamicEffect?: boolean;
    
    dynamicEffect?: {
        
        intensityVariation?: number;
        
        colorVariation?: number;
        
        directionVariation?: number;
        
        speed?: number;
    };
}

export interface AreaLightOptions {
    
    type: AreaLightType;
    
    color?: THREE.ColorRepresentation;
    
    intensity?: number;
    
    castShadow?: boolean;
    
    showHelper?: boolean;
    
    helperColor?: THREE.ColorRepresentation;
}

export interface DiskAreaLightOptions extends AreaLightOptions {
    
    type: AreaLightType.DISK;
    
    radius?: number;
    
    usePhysicalUnits?: boolean;
    
    power?: number;
    
    efficacy?: number;
    
    helperSegments?: number;
}

export interface IESLightOptions {
    
    type?: IESLightType;
    
    color?: THREE.ColorRepresentation;
    
    intensity?: number;
    
    castShadow?: boolean;
    
    distance?: number;
    
    decay?: number;
    
    iesFilePath?: string;
    
    iesData?: string;
    
    iesTexture?: THREE.Texture;
    
    useIESTexture?: boolean;
    
    shadowMapSize?: number;
    
    shadowCameraNear?: number;
    
    shadowCameraFar?: number;
    
    shadowBias?: number;
    
    shadowRadius?: number;
}

export interface SphereAreaLightOptions extends AreaLightOptions {
    
    type: AreaLightType.SPHERE;
    
    radius?: number;
    
    usePhysicalUnits?: boolean;
    
    power?: number;
    
    efficacy?: number;
    
    helperSegments?: number;
}

export interface TubeAreaLightOptions extends AreaLightOptions {
    
    type: AreaLightType.TUBE;
    
    length?: number;
    
    radius?: number;
    
    usePhysicalUnits?: boolean;
    
    power?: number;
    
    efficacy?: number;
    
    helperSegments?: number;
}

export interface MaterialConverterOptions {
    
    deviceCapabilities?: DeviceCapabilities;
    
    preserveOriginal?: boolean;
}

export interface MaterialOptimizerOptions {
    
    deviceCapabilities?: DeviceCapabilities;
    
    enableTextureCompression?: boolean;
    
    enableTextureSizeLimit?: boolean;
    
    maxTextureSize?: number;
    
    enableAnisotropy?: boolean;
    
    maxAnisotropy?: number;
    
    enableMipmap?: boolean;
    
    enableShaderOptimization?: boolean;
}

export interface MaterialSystemOptions {
    
    autoOptimize?: boolean;
    
    autoDowngrade?: boolean;
    
    maxMaterials?: number;
    
    enableCache?: boolean;
}

export interface MaterialOptions {
    
    name?: string;
    
    color?: THREE.Color | string | number;
    
    map?: THREE.Texture;
    
    transparent?: boolean;
    
    opacity?: number;
    
    side?: THREE.Side;
    
    blending?: THREE.Blending;
    
    depthTest?: boolean;
    
    depthWrite?: boolean;
    
    alphaTest?: number;
    
    fog?: boolean;
    
    wireframe?: boolean;
    
    clipIntersection?: boolean;
    
    clippingPlanes?: THREE.Plane[];
    
    customShader?: {
        vertexShader?: string;
        fragmentShader?: string;
        uniforms?: {
            [uniform: string]: THREE.IUniform;
        };
    };
}

export interface PBRMaterialOptions extends MaterialOptions {
    
    metalness?: number;
    
    roughness?: number;
    
    metalnessMap?: THREE.Texture;
    
    roughnessMap?: THREE.Texture;
    
    normalMap?: THREE.Texture;
    
    normalScale?: THREE.Vector2;
    
    envMap?: THREE.Texture;
    
    envMapIntensity?: number;
    
    lightMap?: THREE.Texture;
    
    lightMapIntensity?: number;
    
    aoMap?: THREE.Texture;
    
    aoMapIntensity?: number;
    
    emissiveMap?: THREE.Texture;
    
    emissive?: THREE.Color | string | number;
    
    emissiveIntensity?: number;
    
    displacementMap?: THREE.Texture;
    
    displacementScale?: number;
    
    displacementBias?: number;
}

export interface MeshComponentOptions {
    
    geometry?: THREE.BufferGeometry;
    
    material?: THREE.Material | THREE.Material[];
    
    materialType?: MaterialType;
    
    materialOptions?: MaterialOptions;
    
    visible?: boolean;
    
    receiveShadow?: boolean;
    
    castShadow?: boolean;
    
    frustumCulled?: boolean;
    
    renderOrder?: number;
    
    instanced?: boolean;
    
    instanceCount?: number;
    
    enableLOD?: boolean;
    
    lodLevels?: {
        distance: number;
        geometry: THREE.BufferGeometry;
    }[];
}

export interface AdvancedInstanceData {
    
    position: THREE.Vector3;
    
    quaternion: THREE.Quaternion;
    
    scale: THREE.Vector3;
    
    color?: THREE.Color;
    
    customAttributes?: Record<string, any>;
    
    materialOverrides?: Record<string, any>;
    
    lodConfig?: {
        
        distanceFactor?: number;
        
        enabled?: boolean;
    };
    
    visible?: boolean;
    
    castShadow?: boolean;
    
    receiveShadow?: boolean;
    
    usePhysics?: boolean;
    
    physicsProperties?: {
        
        mass?: number;
        
        isStatic?: boolean;
        
        collisionShape?: 'box' | 'sphere' | 'capsule' | 'cylinder' | 'convexHull' | 'mesh';
        
        friction?: number;
        
        restitution?: number;
    };
    
    userData?: any;
}

export interface InstancedRenderingSystemOptions {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    useFrustumCulling?: boolean;
    
    useOcclusionCulling?: boolean;
    
    useDistanceCulling?: boolean;
    
    maxCullingDistance?: number;
    
    useDynamicBatching?: boolean;
    
    useGPUInstancing?: boolean;
}

export interface EnhancedInstancedRenderingSystemOptions extends InstancedRenderingSystemOptions {
    
    useGPUInstanceUpdate?: boolean;
    
    useInstanceBatching?: boolean;
    
    useInstanceMerging?: boolean;
    
    useInstanceLOD?: boolean;
    
    useInstanceCulling?: boolean;
    
    useInstanceCache?: boolean;
    
    useInstanceSorting?: boolean;
    
    useInstanceGrouping?: boolean;
    
    useInstanceAnimation?: boolean;
    
    useInstancePhysics?: boolean;
    
    useInstanceShadow?: boolean;
    
    useInstanceMaterialVariants?: boolean;
    
    maxBatchSize?: number;
    
    maxInstanceCount?: number;
    
    updateInterval?: number;
    
    useDebugVisualization?: boolean;
}

export interface AdvancedInstancedRenderingSystemOptions extends EnhancedInstancedRenderingSystemOptions {
    
    supportCustomShaders?: boolean;
    
    supportMaterialVariants?: boolean;
    
    supportInstanceLOD?: boolean;
    
    supportInstanceAnimation?: boolean;
    
    supportInstancePhysics?: boolean;
    
    useAdvancedCulling?: boolean;
    
    useInstanceCache?: boolean;
    
    useInstanceMerging?: boolean;
    
    useAutoInstancing?: boolean;
}

export interface MaterialVariant {
    
    id: string;
    
    originalMaterial: THREE.Material;
    
    variantMaterial: THREE.Material;
    
    propertyMap: Record<string, string>;
}

export interface BatchingSystemOptions {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    useStaticBatching?: boolean;
    
    useDynamicBatching?: boolean;
    
    useGPUInstancing?: boolean;
    
    maxBatchSize?: number;
    
    maxInstanceCount?: number;
    
    optimizeGeometry?: boolean;
    
    mergeMaterials?: boolean;
    
    useOctree?: boolean;
}

export interface BatchGroup {
    
    id: string;
    
    name: string;
    
    mesh: THREE.Mesh | THREE.InstancedMesh;
    
    originalMeshes: THREE.Mesh[];
    
    originalEntities: Entity[];
    
    isStatic: boolean;
    
    isInstanced: boolean;
    
    matrices?: THREE.Matrix4[];
    
    colors?: THREE.Color[];
    
    visible: boolean;
    
    isDirty: boolean;
}

export interface CullableComponentOptions {
    
    boundingRadius?: number;
    
    autoComputeBoundingRadius?: boolean;
    
    useBoundingBox?: boolean;
    
    autoComputeBoundingBox?: boolean;
    
    visible?: boolean;
    
    cullable?: boolean;
}

export interface LODSystemOptions {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    useFrustumCheck?: boolean;
    
    useOcclusionCheck?: boolean;
    
    useDistanceCheck?: boolean;
    
    useScreenSizeCheck?: boolean;
    
    useAutoLODGeneration?: boolean;
}

export interface EnhancedLODSystemOptions extends LODSystemOptions {
    
    enableSmoothTransition?: boolean;
    
    transitionDuration?: number;
    
    transitionType?: TransitionType;
    
    useEnhancedGenerator?: boolean;
    
    simplificationAlgorithm?: SimplificationAlgorithm;
    
    useGPU?: boolean;
    
    useAdaptiveLOD?: boolean;
    
    useTextureLOD?: boolean;
    
    useLODCache?: boolean;
    
    maxCacheSize?: number;
}

export interface DynamicLODSystemOptions extends EnhancedLODSystemOptions {
    
    useDynamicLOD?: boolean;
    
    targetFPS?: number;
    
    minFPS?: number;
    
    maxFPS?: number;
    
    adjustmentSensitivity?: number;
    
    adjustmentInterval?: number;
    
    useSmoothTransition?: boolean;
    
    transitionTime?: number;
    
    qualityLevel?: number;
    
    useAutoQualityAdjustment?: boolean;
    
    useDistanceOffset?: boolean;
    
    distanceOffsetFactor?: number;
}

export interface InstanceData {
    
    position: THREE.Vector3;
    
    quaternion: THREE.Quaternion;
    
    scale: THREE.Vector3;
    
    color?: THREE.Color;
    
    userData?: any;
}

export interface EnhancedInstanceData extends InstanceData {
    
    id?: string;
    
    rotation?: THREE.Quaternion;
    
    customAttributes?: Map<string, number | number[] | THREE.Vector2 | THREE.Vector3 | THREE.Vector4 | THREE.Color>;
    
    animationState?: {
        clipName: string;
        time: number;
        weight: number;
    };
    
    physicsProperties?: {
        velocity: THREE.Vector3;
        angularVelocity: THREE.Vector3;
        mass: number;
    };
    
    needsUpdate?: boolean;
    
    visible?: boolean;
    
    castShadow?: boolean;
    
    receiveShadow?: boolean;
    
    renderOrder?: number;
    
    layer?: number;
    
    userData?: any;
}

export interface InstanceBatchGroup {
    
    id: string;
    
    geometry: THREE.BufferGeometry;
    
    material: THREE.Material;
    
    instancedMesh: THREE.InstancedMesh;
    
    instances: EnhancedInstanceData[];
    
    instanceToIndex: Map<string, number>;
    
    availableIndices: number[];
    
    needsUpdate: boolean;
    
    visible: boolean;
    
    castShadow: boolean;
    
    receiveShadow: boolean;
    
    renderOrder: number;
    
    layer: number;
    
    userData: any;
    
    customAttributeBuffers: Map<string, THREE.InstancedBufferAttribute>;
}

export interface LODGeneratorOptions {
    
    highDetailRatio?: number;
    
    mediumDetailRatio?: number;
    
    lowDetailRatio?: number;
    
    veryLowDetailRatio?: number;
    
    preserveUVs?: boolean;
    
    preserveNormals?: boolean;
    
    preserveColors?: boolean;
}

export interface EnhancedLODGeneratorOptions extends LODGeneratorOptions {
    
    algorithm?: SimplificationAlgorithm;
    
    useGPU?: boolean;
    
    preserveFeatures?: boolean;
    
    useProgressiveMesh?: boolean;
    
    adaptiveSimplification?: boolean;
    
    useTextureLOD?: boolean;
}

export interface LODGeneratorResult {
    
    original: THREE.Mesh;
    
    high: THREE.Mesh;
    
    medium: THREE.Mesh;
    
    low: THREE.Mesh;
    
    veryLow: THREE.Mesh;
}

export interface EnhancedLODGeneratorResult extends LODGeneratorResult {
    
    progressiveMesh?: THREE.Mesh;
    
    textureLODs?: Map<LODLevel, THREE.Texture>;
    
    stats: {
        
        originalVertexCount: number;
        
        simplifiedVertexCounts: Map<LODLevel, number>;
        
        simplificationTime: number;
        
        memoryUsage: number;
    };
}

export interface OcclusionCullingSystemOptions {
    
    enabled?: boolean;
    
    algorithm?: OcclusionCullingAlgorithm;
    
    precision?: number;
    
    useHierarchicalZBuffer?: boolean;
    
    useOcclusionQuery?: boolean;
    
    useGPU?: boolean;
    
    useConservativeOcclusion?: boolean;
    
    useTemporalCoherence?: boolean;
    
    useDebugVisualization?: boolean;
    
    occlusionQueryInterval?: number;
    
    occlusionQueryTimeout?: number;
    
    occlusionQueryBatchSize?: number;
}

export interface EnhancedOcclusionCullingSystemOptions extends OcclusionCullingSystemOptions {
    
    useAdaptiveAlgorithm?: boolean;
    
    useMultiLevelCulling?: boolean;
    
    usePredictiveCulling?: boolean;
    
    useTemporalCoherence?: boolean;
    
    useGPUAcceleration?: boolean;
    
    useConservativeCulling?: boolean;
    
    collectStats?: boolean;
    
    useAutoOptimization?: boolean;
    
    autoOptimizationInterval?: number;
}

export interface OcclusionCullingStats {
    
    totalObjects: number;
    
    culledObjects: number;
    
    cullingRate: number;
    
    cullingTime: number;
    
    renderTime: number;
    
    totalTime: number;
    
    algorithm: OcclusionCullingAlgorithm;
    
    timestamp: number;
}

export interface FrustumCullingSystemOptions {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    useOctree?: boolean;
    
    useOcclusionCulling?: boolean;
    
    useBoundingBoxCulling?: boolean;
    
    useBoundingSphereCulling?: boolean;
    
    useDistanceCulling?: boolean;
    
    maxCullingDistance?: number;
}

export interface GPUInstanceUpdaterOptions {
    
    useWebGL2?: boolean;
    
    useComputeShader?: boolean;
    
    useBatching?: boolean;
    
    batchSize?: number;
    
    useDoubleBuffering?: boolean;
    
    useParallelUpdate?: boolean;
    
    useDebugVisualization?: boolean;
}

export interface InstancedComponentOptions {
    
    originalMesh?: THREE.Mesh;
    
    maxInstanceCount?: number;
    
    useColor?: boolean;
    
    dynamic?: boolean;
    
    visible?: boolean;
}

export interface LODLevelConfig {
    
    level: LODLevel;
    
    distance: number;
    
    originalDistance?: number;
    
    mesh: THREE.Mesh | THREE.Group;
    
    visible?: boolean;
}

export interface LODComponentOptions {
    
    levels?: LODLevelConfig[];
    
    boundingRadius?: number;
    
    autoComputeBoundingRadius?: boolean;
    
    autoSortLevels?: boolean;
}

export interface LODTransitionControllerOptions {
    
    duration?: number;
    
    type?: TransitionType;
    
    useEasing?: boolean;
    
    easingFunction?: (t: number) => number;
    
    debug?: boolean;
}

export interface TransitionState {
    
    fromMesh: THREE.Mesh;
    
    toMesh: THREE.Mesh;
    
    fromLevel: LODLevel;
    
    toLevel: LODLevel;
    
    startTime: number;
    
    duration: number;
    
    type: TransitionType;
    
    progress: number;
    
    completed: boolean;
    
    onComplete?: () => void;
    
    transitionMesh?: THREE.Mesh;
}

export interface OcclusionCullingResult {
    
    visibleCount: number;
    
    invisibleCount: number;
    
    queryingCount: number;
    
    unknownCount: number;
    
    totalCount: number;
    
    cullingRate: number;
    
    queryTime: number;
}

export interface PortalCullingSystemOptions {
    
    enabled?: boolean;
    
    useDebugVisualization?: boolean;
    
    useConservativeCulling?: boolean;
    
    useTemporalCoherence?: boolean;
    
    usePortalMerging?: boolean;
    
    usePortalCache?: boolean;
    
    portalCacheUpdateInterval?: number;
    
    useAntiAliasing?: boolean;
}

export interface Room {
    
    id: string;
    
    geometry: THREE.BufferGeometry;
    
    position: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    scale: THREE.Vector3;
    
    matrix: THREE.Matrix4;
    
    boundingBox: THREE.Box3;
    
    boundingSphere: THREE.Sphere;
    
    portals: Portal[];
    
    entities: Entity[];
    
    visible: boolean;
    
    visited: boolean;
    
    depth: number;
}

export interface Portal {
    
    id: string;
    
    geometry: THREE.BufferGeometry;
    
    position: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    scale: THREE.Vector3;
    
    matrix: THREE.Matrix4;
    
    normal: THREE.Vector3;
    
    center: THREE.Vector3;
    
    halfWidth: number;
    
    halfHeight: number;
    
    roomA: Room;
    
    roomB: Room;
    
    visible: boolean;
    
    bidirectional: boolean;
    
    open: boolean;
    
    openness: number;
    
    frustum?: THREE.Frustum;
    
    plane?: THREE.Plane;
}

export interface PortalCullingResult {
    
    visibleRoomCount: number;
    
    invisibleRoomCount: number;
    
    visiblePortalCount: number;
    
    invisiblePortalCount: number;
    
    visibleEntityCount: number;
    
    invisibleEntityCount: number;
    
    totalRoomCount: number;
    
    totalPortalCount: number;
    
    totalEntityCount: number;
    
    roomCullingRate: number;
    
    portalCullingRate: number;
    
    entityCullingRate: number;
    
    cullingTime: number;
}

export interface PostProcessingEffectOptions {
    
    name: string;
    
    enabled?: boolean;
}

export interface BloomEffectOptions extends PostProcessingEffectOptions {
    
    strength?: number;
    
    radius?: number;
    
    threshold?: number;
    
    selectiveBloom?: boolean;
}

export interface ChromaticAberrationEffectOptions extends PostProcessingEffectOptions {
    
    offset?: THREE.Vector2;
    
    radialMode?: boolean;
    
    intensity?: number;
}

export interface ColorCorrectionEffectOptions extends PostProcessingEffectOptions {
    
    brightness?: number;
    
    contrast?: number;
    
    saturation?: number;
    
    hue?: number;
    
    gamma?: number;
    
    temperature?: number;
    
    tint?: number;
    
    shadowTint?: THREE.Color;
    
    highlightTint?: THREE.Color;
    
    useLUT?: boolean;
    
    lutTexture?: THREE.Texture;
    
    lutIntensity?: number;
}

export interface DepthOfFieldEffectOptions extends PostProcessingEffectOptions {
    
    focus?: number;
    
    aperture?: number;
    
    maxBlur?: number;
    
    useDepthMap?: boolean;
}

export interface FXAAEffectOptions extends PostProcessingEffectOptions {
    
    edgeThreshold?: number;
    
    edgeThresholdMin?: number;
}

export interface GlitchEffectOptions extends PostProcessingEffectOptions {
    
    goWild?: boolean;
    
    amount?: number;
    
    interval?: number;
    
    duration?: number;
}

export interface MotionBlurEffectOptions extends PostProcessingEffectOptions {
    
    intensity?: number;
    
    samples?: number;
    
    useCameraMotion?: boolean;
    
    useObjectMotion?: boolean;
}

export interface NoiseEffectOptions extends PostProcessingEffectOptions {
    
    intensity?: number;
    
    colored?: boolean;
    
    animated?: boolean;
    
    premultiply?: boolean;
}

export interface PixelationEffectOptions extends PostProcessingEffectOptions {
    
    pixelSize?: number;
    
    preserveAspect?: boolean;
    
    pixelBorder?: boolean;
    
    borderColor?: THREE.Color;
    
    borderWidth?: number;
}

export interface ScanlineEffectOptions extends PostProcessingEffectOptions {
    
    count?: number;
    
    intensity?: number;
    
    animated?: boolean;
    
    speed?: number;
    
    color?: THREE.Color;
    
    noise?: boolean;
    
    noiseIntensity?: number;
}

export interface SSAOEffectOptions extends PostProcessingEffectOptions {
    
    output?: SSAOOutputMode;
    
    kernelRadius?: number;
    
    minDistance?: number;
    
    maxDistance?: number;
    
    aoClamp?: number;
    
    lumInfluence?: number;
}

export interface SSGIEffectOptions extends PostProcessingEffectOptions {
    
    intensity?: number;
    
    radius?: number;
    
    bias?: number;
    
    samples?: number;
    
    falloff?: number;
    
    useNormalMap?: boolean;
    
    useDepthMap?: boolean;
    
    denoiseIterations?: number;
    
    denoiseKernel?: number;
    
    denoiseDiffuse?: number;
    
    denoiseSpecular?: number;
    
    phi?: number;
    
    lumaPhi?: number;
    
    depthPhi?: number;
    
    normalPhi?: number;
    
    roughnessPhi?: number;
    
    specularPhi?: number;
    
    envBlur?: number;
    
    importanceSampling?: boolean;
    
    refineSteps?: number;
    
    resolutionScale?: number;
    
    missedRays?: boolean;
}

export interface SSREffectOptions extends PostProcessingEffectOptions {
    
    intensity?: number;
    
    maxSteps?: number;
    
    maxDistance?: number;
    
    stride?: number;
    
    roughness?: number;
    
    thickness?: number;
    
    useNormalMap?: boolean;
    
    useRoughnessMap?: boolean;
    
    denoiseIterations?: number;
    
    denoiseKernel?: number;
    
    denoiseDiffuse?: number;
    
    denoiseSpecular?: number;
    
    radius?: number;
    
    phi?: number;
    
    lumaPhi?: number;
    
    depthPhi?: number;
    
    normalPhi?: number;
    
    roughnessPhi?: number;
    
    specularPhi?: number;
    
    envBlur?: number;
    
    importanceSampling?: boolean;
    
    refineSteps?: number;
    
    resolutionScale?: number;
    
    missedRays?: boolean;
}

export interface ToneMappingEffectOptions extends PostProcessingEffectOptions {
    
    type?: ToneMappingType;
    
    exposure?: number;
    
    gamma?: number;
    
    contrast?: number;
    
    saturation?: number;
    
    brightness?: number;
}

export interface VignetteEffectOptions extends PostProcessingEffectOptions {
    
    offset?: number;
    
    darkness?: number;
    
    eskil?: boolean;
    
    color?: THREE.Color;
}

export interface PostProcessingSystemOptions {
    
    enabled?: boolean;
    
    autoResize?: boolean;
    
    renderTargetOptions?: {
        
        width?: number;
        
        height?: number;
        
        samples?: number;
        
        depthTexture?: boolean;
        
        floatTexture?: boolean;
    };
}

export interface RendererOptions {
    
    canvas?: HTMLCanvasElement | string;
    
    width?: number;
    
    height?: number;
    
    antialias?: boolean;
    
    alpha?: boolean;
    
    depth?: boolean;
    
    stencil?: boolean;
    
    logarithmicDepthBuffer?: boolean;
    
    autoClear?: boolean;
    
    autoResize?: boolean;
    
    pixelRatio?: number;
    
    shadowMapType?: THREE.ShadowMapType;
    
    shadows?: boolean;
    
    toneMapping?: THREE.ToneMapping;
    
    toneMappingExposure?: number;
    
    outputColorSpace?: THREE.ColorSpace;
}

export interface CSMParams {
    light?: THREE.DirectionalLight;
    cascades?: number;
    maxFar?: number;
    mode?: string;
    shadowMapSize?: number;
    shadowBias?: number;
    lightDirection?: THREE.Vector3;
    lightDirectionUp?: THREE.Vector3;
    lightIntensity?: number;
    lightColor?: THREE.ColorRepresentation;
    lightNear?: number;
    lightFar?: number;
    lightMargin?: number;
    customSplitsCallback?: (amount: number, near: number, far: number, target: number[]) => void;
    fade?: boolean;
}

export interface FrustumVertices {
    near: THREE.Vector3[];
    far: THREE.Vector3[];
}

export interface FrustumParams {
    projectionMatrix?: THREE.Matrix4;
    maxFar?: number;
}

export interface ShadowSystemOptions {
    
    enabled?: boolean;
    
    useCSM?: boolean;
    
    cascades?: number;
    
    shadowMapSize?: number;
    
    shadowBias?: number;
    
    maxShadowDistance?: number;
    
    fade?: boolean;
}

export interface ShadowComponent {
    
    castShadow: boolean;
    
    receiveShadow: boolean;
}

export interface EnhancedWaterSurfaceRendererConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    reflectionMapResolution?: number;
    
    refractionMapResolution?: number;
    
    enableReflection?: boolean;
    
    enableRefraction?: boolean;
    
    enableCaustics?: boolean;
    
    enableUnderwaterFog?: boolean;
    
    enableUnderwaterDistortion?: boolean;
    
    enablePerformanceMonitoring?: boolean;
    
    useLowQualityMode?: boolean;
}

export interface OptimizedWaterMaterialConfig {
    
    color?: THREE.Color;
    
    opacity?: number;
    
    reflectivity?: number;
    
    refractionRatio?: number;
    
    waveStrength?: number;
    
    waveSpeed?: number;
    
    waveScale?: number;
    
    waveDirection?: THREE.Vector2;
    
    depth?: number;
    
    depthColor?: THREE.Color;
    
    shallowColor?: THREE.Color;
    
    normalMap?: THREE.Texture;
    
    reflectionMap?: THREE.Texture;
    
    refractionMap?: THREE.Texture;
    
    depthMap?: THREE.Texture;
    
    causticsMap?: THREE.Texture;
    
    foamMap?: THREE.Texture;
    
    enableReflection?: boolean;
    
    enableRefraction?: boolean;
    
    enableCaustics?: boolean;
    
    enableFoam?: boolean;
    
    enableUnderwaterFog?: boolean;
    
    enableUnderwaterDistortion?: boolean;
    
    transparent?: boolean;
    
    side?: THREE.Side;
    
    depthWrite?: boolean;
    
    qualityLevel?: number;
}

export interface UnderwaterParticleConfig {
    
    type: UnderwaterParticleType;
    
    count: number;
    
    size: number | [number, number];
    
    color?: THREE.Color | number | string;
    
    opacity?: number;
    
    lifetime?: number | [number, number];
    
    speed?: number | [number, number];
    
    acceleration?: THREE.Vector3;
    
    rotation?: boolean;
    
    rotationSpeed?: number | [number, number];
    
    texture?: THREE.Texture | string;
    
    blending?: THREE.Blending;
    
    emissionArea?: {
        
        shape: 'box' | 'sphere' | 'cylinder';
        
        size: THREE.Vector3 | number;
        
        position?: THREE.Vector3;
    };
    
    customShader?: {
        
        vertex?: string;
        
        fragment?: string;
        
        uniforms?: {
            [key: string]: {
                value: any;
            };
        };
    };
}

export interface UnderwaterParticleSystemConfig {
    
    enabled?: boolean;
    
    maxParticles?: number;
    
    useGPU?: boolean;
    
    autoAdjustPerformance?: boolean;
    
    debug?: boolean;
}

export interface UnderwaterVolumetricLightConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    density?: number;
    
    decay?: number;
    
    weight?: number;
    
    exposure?: number;
    
    samples?: number;
    
    color?: THREE.Color;
    
    useDebugVisualization?: boolean;
}

export interface WaterChunk {
    
    id: string;
    
    name: string;
    
    boundingBox: THREE.Box3;
    
    boundingSphere: THREE.Sphere;
    
    center: THREE.Vector3;
    
    size: THREE.Vector3;
    
    level: number;
    
    visible: boolean;
    
    loaded: boolean;
    
    loading: boolean;
    
    progress: number;
    
    mesh?: THREE.Mesh;
    
    parent: WaterChunk | null;
    
    children: WaterChunk[];
    
    neighbors: WaterChunk[];
    
    userData: any;
}

export interface WaterChunkSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    chunkSize?: number;
    
    maxLODLevel?: number;
    
    useLOD?: boolean;
    
    lodDistances?: number[];
    
    useFrustumCulling?: boolean;
    
    useOctree?: boolean;
    
    useDebugVisualization?: boolean;
    
    loadDistance?: number;
    
    unloadDistance?: number;
}

export interface WaterEffectInstance {
    
    id: string;
    
    type: WaterEffectType;
    
    position: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    scale: THREE.Vector3;
    
    color: THREE.Color;
    
    opacity: number;
    
    lifetime: number;
    
    age: number;
    
    active: boolean;
    
    userData: any;
}

export interface WaterInstancedRendererConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    maxInstances?: number;
    
    useDebugVisualization?: boolean;
    
    useGPUCulling?: boolean;
    
    useInstancedShadows?: boolean;
}

export interface WaterLightingSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enableReflection?: boolean;
    
    enableRefraction?: boolean;
    
    enableCaustics?: boolean;
    
    enableVolumetricLight?: boolean;
    
    enableUnderwaterFog?: boolean;
    
    enablePerformanceMonitoring?: boolean;
    
    reflectionMapResolution?: number;
    
    refractionMapResolution?: number;
    
    causticsMapResolution?: number;
    
    volumetricLightMapResolution?: number;
    
    reflectionIntensity?: number;
    
    refractionIntensity?: number;
    
    causticsIntensity?: number;
    
    volumetricLightIntensity?: number;
    
    underwaterFogIntensity?: number;
    
    underwaterFogColor?: THREE.Color;
    
    underwaterFogDensity?: number;
    
    enableHighQualityReflection?: boolean;
    
    enableHighQualityRefraction?: boolean;
    
    enableHighQualityCaustics?: boolean;
    
    enableHighQualityVolumetricLight?: boolean;
}

export interface WaterLODConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    lodDistances?: number[];
    
    useSmoothTransition?: boolean;
    
    transitionTime?: number;
    
    useDebugVisualization?: boolean;
}

export interface WaterMaterialConfig {
    
    type?: WaterBodyType;
    
    color?: THREE.Color | number | string;
    
    opacity?: number;
    
    reflectivity?: number;
    
    refractionRatio?: number;
    
    waveStrength?: number;
    
    waveSpeed?: number;
    
    waveScale?: number;
    
    waveDirection?: THREE.Vector2;
    
    depth?: number;
    
    depthColor?: THREE.Color | number | string;
    
    shallowColor?: THREE.Color | number | string;
    
    normalMap?: THREE.Texture;
    
    reflectionMap?: THREE.Texture;
    
    refractionMap?: THREE.Texture;
    
    depthMap?: THREE.Texture;
    
    causticsMap?: THREE.Texture;
    
    foamMap?: THREE.Texture;
    
    enableReflection?: boolean;
    
    enableRefraction?: boolean;
    
    enableCaustics?: boolean;
    
    enableFoam?: boolean;
    
    enableUnderwaterFog?: boolean;
    
    enableUnderwaterDistortion?: boolean;
}

export interface WaterMaterialPreset {
    
    id: string;
    
    name: string;
    
    description: string;
    
    category: string;
    
    tags: string[];
    
    thumbnail?: string;
    
    author?: string;
    
    config: WaterMaterialConfig;
}

export interface WaterMaterialPresetManagerConfig {
    
    debug?: boolean;
    
    loadFromServer?: boolean;
    
    serverUrl?: string;
}

export interface WaterMobileOptimizationSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enableDebugVisualization?: boolean;
    
    enablePerformanceMonitoring?: boolean;
    
    enableAutoOptimization?: boolean;
    
    enableBatteryOptimization?: boolean;
    
    enableTemperatureOptimization?: boolean;
    
    targetFPS?: number;
    
    minAcceptableFPS?: number;
    
    defaultPerformanceLevel?: DevicePerformanceLevel;
    
    lowBatteryThreshold?: number;
    
    highTemperatureThreshold?: number;
}

export interface WaterRendererConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enableReflection?: boolean;
    
    enableRefraction?: boolean;
    
    enableCaustics?: boolean;
    
    enableUnderwaterFog?: boolean;
    
    enableUnderwaterDistortion?: boolean;
    
    enableDepthTest?: boolean;
    
    enablePerformanceMonitoring?: boolean;
    
    reflectionMapResolution?: number;
    
    refractionMapResolution?: number;
    
    useDeferredRendering?: boolean;
    
    useScreenSpaceReflection?: boolean;
}

export interface WaterRenderOptimizationConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enablePerformanceMonitoring?: boolean;
    
    enableAutoOptimization?: boolean;
    
    enableBatteryOptimization?: boolean;
    
    enableTemperatureOptimization?: boolean;
    
    enableNetworkOptimization?: boolean;
    
    enableMemoryOptimization?: boolean;
    
    enableGPUOptimization?: boolean;
    
    enableCPUOptimization?: boolean;
    
    enableMobileOptimization?: boolean;
    
    enableDesktopOptimization?: boolean;
    
    enableVROptimization?: boolean;
    
    enableAROptimization?: boolean;
    
    enableDebugVisualization?: boolean;
    
    defaultPerformanceLevel?: DevicePerformanceLevel;
}

export interface WaterSurfaceRendererConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    enableReflection?: boolean;
    
    enableRefraction?: boolean;
    
    enableCaustics?: boolean;
    
    enableUnderwaterFog?: boolean;
    
    enableUnderwaterDistortion?: boolean;
    
    enableDepthTest?: boolean;
    
    enablePerformanceMonitoring?: boolean;
    
    reflectionMapResolution?: number;
    
    refractionMapResolution?: number;
    
    useScreenSpaceReflection?: boolean;
    
    useHighQualityWaves?: boolean;
    
    useGPUAcceleration?: boolean;
}

export interface SceneChunk {
    
    id: string;
    
    name: string;
    
    boundingBox: THREE.Box3;
    
    center: THREE.Vector3;
    
    size: THREE.Vector3;
    
    level: number;
    
    resources: string[];
    
    priority: ResourcePriority;
    
    visible: boolean;
    
    loaded: boolean;
    
    loading: boolean;
    
    progress: number;
    
    children: string[];
    
    parent: string | null;
    
    neighbors: string[];
    
    userData: any;
}

export interface SceneLoadingSystemOptions {
    
    maxConcurrentLoads?: number;
    
    maxMemoryUsage?: number;
    
    preloadDistance?: number;
    
    unloadDistance?: number;
    
    usePredictiveLoading?: boolean;
    
    useCache?: boolean;
    
    useCompression?: boolean;
    
    useDebugVisualization?: boolean;
    
    loadTimeout?: number;
    
    retryCount?: number;
    
    retryDelay?: number;
}

export interface ChunkedSceneLoadingSystemOptions extends SceneLoadingSystemOptions {
    
    useQuadtree?: boolean;
    
    useOctree?: boolean;
    
    useLOD?: boolean;
    
    usePredictiveLoading?: boolean;
    
    useProgressiveLoading?: boolean;
    
    useStreamingLoading?: boolean;
    
    useMemoryManagement?: boolean;
    
    usePriorityQueue?: boolean;
    
    useCache?: boolean;
    
    usePreloading?: boolean;
    
    useBackgroundLoading?: boolean;
    
    useDebugVisualization?: boolean;
    
    chunkSize?: number;
    
    maxChunkLevel?: number;
    
    loadDistance?: number;
    
    unloadDistance?: number;
    
    preloadDistance?: number;
    
    maxMemoryUsage?: number;
    
    maxConcurrentLoads?: number;
}

export interface SceneImportOptions {
    
    mergeWithExisting?: boolean;
    
    keepExistingEntities?: boolean;
    
    importMaterials?: boolean;
    
    importTextures?: boolean;
    
    importAnimations?: boolean;
    
    importCameras?: boolean;
    
    importLights?: boolean;
    
    computeNormals?: boolean;
    
    computeTangents?: boolean;
    
    optimizeGeometry?: boolean;
    
    convertToInstanced?: boolean;
    
    generateLOD?: boolean;
    
    applyTransforms?: boolean;
    
    metadata?: Record<string, any>;
}

export interface SceneExportOptions {
    
    format?: SceneFormat;
    
    exportMaterials?: boolean;
    
    exportTextures?: boolean;
    
    exportAnimations?: boolean;
    
    exportCameras?: boolean;
    
    exportLights?: boolean;
    
    binary?: boolean;
    
    compressed?: boolean;
    
    prettyPrint?: boolean;
    
    embedResources?: boolean;
    
    metadata?: Record<string, any>;
}

export interface SceneMergeOptions {
    
    preserveOriginalScenes?: boolean;
    
    mergeMaterials?: boolean;
    
    mergeGeometries?: boolean;
    
    mergeTextures?: boolean;
    
    optimizeMergedScene?: boolean;
    
    applyTransforms?: boolean;
    
    regenerateUUIDs?: boolean;
    
    preserveHierarchy?: boolean;
    
    useInstancing?: boolean;
    
    metadata?: Record<string, any>;
}

export interface SceneSplitOptions {
    
    splitMethod?: 'hierarchy' | 'spatial' | 'material' | 'custom';
    
    preserveOriginalScene?: boolean;
    
    optimizeSplitScenes?: boolean;
    
    regenerateUUIDs?: boolean;
    
    customSplitFunction?: (entity: Entity) => number;
    
    spatialGridSize?: number;
    
    maxSceneCount?: number;
    
    metadata?: Record<string, any>;
}

export interface SceneMergeResult {
    
    mergedScene: Scene;
    
    entityMap: Map<string, string>;
    
    componentMap: Map<string, string>;
    
    materialMap: Map<string, string>;
    
    geometryMap: Map<string, string>;
    
    textureMap: Map<string, string>;
}

export interface SceneSplitResult {
    
    splitScenes: Scene[];
    
    entityMap: Map<string, string[]>;
    
    componentMap: Map<string, string[]>;
    
    materialMap: Map<string, string[]>;
    
    geometryMap: Map<string, string[]>;
    
    textureMap: Map<string, string[]>;
}

export interface SceneGraphNode {
    
    id: string;
    
    name: string;
    
    type: string;
    
    children: SceneGraphNode[];
    
    components: string[];
    
    visible: boolean;
    
    locked: boolean;
    
    expanded?: boolean;
    
    userData?: Record<string, any>;
}

export interface SceneGraphQueryOptions {
    
    includeInvisible?: boolean;
    
    includeLocked?: boolean;
    
    recursive?: boolean;
    
    maxDepth?: number;
    
    componentFilter?: string[];
    
    nameFilter?: string | RegExp;
    
    tagFilter?: string[];
    
    customFilter?: (node: SceneGraphNode) => boolean;
}

export interface TemplateVariant {
    
    id: string;
    
    name: string;
    
    description?: string;
    
    thumbnailUrl?: string;
    
    parameterValues: Record<string, any>;
    
    createdAt: Date;
    
    updatedAt: Date;
    
    userData?: Record<string, any>;
}

export interface SceneSerializedData {
    
    version: string;
    
    id: string;
    
    name: string;
    
    entities?: any[];
    
    skybox?: any;
    
    ambientLight?: any;
    
    fog?: any;
    
    metadata?: Record<string, any>;
}

export interface TemplateParameter {
    
    id: string;
    
    name: string;
    
    description?: string;
    
    type: TemplateParameterType | string;
    
    defaultValue: any;
    
    min?: number;
    
    max?: number;
    
    step?: number;
    
    options?: {
        value: any;
        label: string;
    }[];
    
    required?: boolean;
    
    readonly?: boolean;
    
    hidden?: boolean;
    
    showIf?: string;
    
    validation?: {
        
        pattern?: string;
        
        validator?: string;
        
        message?: string;
    };
    
    group?: string;
    
    order?: number;
    
    unit?: string;
    
    renderer?: string;
    
    editor?: string;
    
    userData?: Record<string, any>;
}

export interface SceneTemplate {
    
    id: string;
    
    name: string;
    
    description?: string;
    
    thumbnailUrl?: string;
    
    serializedData: SceneSerializedData;
    
    parameters: TemplateParameter[];
    
    variants?: TemplateVariant[];
    
    defaultVariantId?: string;
    
    category?: string;
    
    tags?: string[];
    
    version?: string;
    
    author?: string;
    
    license?: string;
    
    createdAt: Date;
    
    updatedAt: Date;
    
    userData?: Record<string, any>;
}

export interface SceneInstance {
    
    id: string;
    
    name: string;
    
    templateId: string;
    
    variantId?: string;
    
    visible: boolean;
    
    position: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    scale: THREE.Vector3;
    
    parameters: Record<string, any>;
    
    rootEntityId?: string;
    
    locked?: boolean;
    
    isStatic?: boolean;
    
    layer?: string;
    
    tags?: string[];
    
    createdAt: Date;
    
    updatedAt: Date;
    
    userData?: Record<string, any>;
}

export interface SceneInstanceManagerOptions {
    
    sceneSerializer?: SceneSerializer;
    
    enableResourceSharing?: boolean;
    
    debug?: boolean;
}

export interface SceneLayerOptions {
    
    id: string;
    
    name: string;
    
    type?: SceneLayerType;
    
    visible?: boolean;
    
    locked?: boolean;
    
    excludeFromRender?: boolean;
    
    excludeFromPhysics?: boolean;
    
    excludeFromRaycast?: boolean;
    
    order?: number;
    
    tags?: string[];
    
    color?: THREE.Color;
    
    parentId?: string;
    
    expanded?: boolean;
    
    userData?: Record<string, any>;
}

export interface SceneLayerManagerOptions {
    
    createDefaultLayers?: boolean;
    
    defaultLayerCount?: number;
}

export interface SceneLayerQueryOptions {
    
    includeInvisible?: boolean;
    
    includeLocked?: boolean;
    
    tagFilter?: string[];
    
    nameFilter?: string | RegExp;
    
    customFilter?: (layer: SceneLayer) => boolean;
}

export interface Resource {
    
    id: string;
    
    url: string;
    
    type: ResourceType;
    
    priority: ResourcePriority;
    
    state: ResourceState;
    
    data: any;
    
    size: number;
    
    progress: number;
    
    startTime: number;
    
    endTime: number;
    
    lastAccessTime: number;
    
    referenceCount: number;
    
    persistent: boolean;
    
    preload: boolean;
    
    dependencies: string[];
    
    userData: any;
}

export interface SceneRegion {
    
    id: string;
    
    name: string;
    
    boundingBox: THREE.Box3;
    
    resources: string[];
    
    priority: ResourcePriority;
    
    visible: boolean;
    
    loaded: boolean;
    
    userData: any;
}

export interface LoadTask {
    
    id: string;
    
    resourceId: string;
    
    priority: ResourcePriority;
    
    state: ResourceState;
    
    loader: THREE.Loader;
    
    progress: number;
    
    startTime: number;
    
    endTime: number;
    
    retryCount: number;
    
    error: Error | null;
    
    cancel: () => void;
    
    onComplete: (resource: Resource) => void;
    
    onProgress: (progress: number) => void;
    
    onError: (error: Error) => void;
}

export interface SceneTransitionOptions {
    
    type: SceneTransitionType;
    
    duration?: number;
    
    direction?: 'left' | 'right' | 'up' | 'down';
    
    easing?: string;
    
    customTransition?: (fromScene: Scene | null, toScene: Scene, progress: number, onComplete: () => void) => void;
}

export interface SceneLoadOptions {
    
    setActive?: boolean;
    
    showLoadingScreen?: boolean;
    
    preloadAssets?: boolean;
    
    initializeSceneGraph?: boolean;
    
    transition?: SceneTransitionOptions;
    
    onLoaded?: (scene: Scene) => void;
    
    onProgress?: (progress: number) => void;
    
    onError?: (error: Error) => void;
}

export interface SceneManagerOptions {
    
    world: World;
    
    assetManager?: AssetManager;
    
    defaultTransition?: SceneTransitionOptions;
    
    enableSceneCache?: boolean;
    
    maxSceneCacheCount?: number;
}

export interface SceneResourceInfo {
    
    id: string;
    
    type: SceneResourceType;
    
    url: string;
    
    priority?: number;
    
    size?: number;
    
    entityId?: string;
    
    componentType?: string;
    
    required?: boolean;
}

export interface ScenePreloaderOptions {
    
    assetManager: AssetManager;
    
    autoAnalyzeResources?: boolean;
    
    autoRegisterResources?: boolean;
    
    maxConcurrentLoads?: number;
    
    debug?: boolean;
}

export interface ScenePreloadProgressInfo {
    
    sceneId: string;
    
    loaded: number;
    
    total: number;
    
    progress: number;
    
    loadedResources: string[];
    
    failedResources: string[];
    
    currentResource?: string;
    
    loadedBytes: number;
    
    totalBytes: number;
}

export interface PreloadConfig {
    
    enabled: boolean;
    
    priority: number;
    
    strategyType: PreloadStrategyType;
    
    distance: number;
    
    directionAngle?: number;
    
    predictionTime?: number;
    
    includeChildren: boolean;
    
    includeMaterials: boolean;
    
    includeTextures: boolean;
    
    includeAudio: boolean;
    
    includeModels: boolean;
    
    useLOD?: boolean;
    
    useTextureCompression?: boolean;
    
    useGeometrySimplification?: boolean;
    
    useInstancing?: boolean;
    
    maxConcurrentLoads?: number;
    
    memoryLimit?: number;
    
    userData?: Record<string, any>;
}

export interface ResourceDependencyInfo {
    
    id: string;
    
    url: string;
    
    type: string;
    
    dependencies: string[];
    
    size: number;
    
    loaded: boolean;
    
    loading: boolean;
    
    priority: number;
}

export interface PreloadRegion {
    
    id: string;
    
    name: string;
    
    position: THREE.Vector3;
    
    radius: number;
    
    config: PreloadConfig;
    
    entityIds: string[];
    
    resourceIds: string[];
    
    loaded: boolean;
    
    progress: number;
}

export interface ScenePreloadManagerOptions {
    
    assetManager: AssetManager;
    
    scenePreloader?: ScenePreloader;
    
    enableAutoPreload?: boolean;
    
    enableDependencyAnalysis?: boolean;
    
    enableOnDemandLoading?: boolean;
    
    debug?: boolean;
}

export interface SceneSerializeOptions {
    
    includeEntities?: boolean;
    
    includeComponents?: boolean;
    
    includeSkybox?: boolean;
    
    includeAmbientLight?: boolean;
    
    includeFog?: boolean;
    
    prettyPrint?: boolean;
    
    includeMetadata?: boolean;
    
    metadata?: Record<string, any>;
}

export interface SceneDeserializeOptions {
    
    includeEntities?: boolean;
    
    includeComponents?: boolean;
    
    includeSkybox?: boolean;
    
    includeAmbientLight?: boolean;
    
    includeFog?: boolean;
    
    keepExistingEntities?: boolean;
    
    mergeWithExisting?: boolean;
    
    componentDeserializers?: Map<string, (data: any, entity: Entity) => Component | null>;
}

export interface SkyboxOptions {
    
    type: SkyboxType;
    
    cubemapPaths?: string[];
    
    equirectangularPath?: string;
    
    proceduralParams?: {
        
        topColor?: THREE.Color | string | number;
        
        bottomColor?: THREE.Color | string | number;
        
        exponent?: number;
    };
    
    rotate?: boolean;
    
    rotationSpeed?: number;
}

export interface AssetMetadata {
    
    id: string;
    
    name: string;
    
    type: 'model' | 'texture' | 'material' | 'environment' | 'animation';
    
    category: string;
    
    subcategory?: string;
    
    tags: string[];
    
    description: string;
    
    filePath: string;
    
    fileSize: number;
    
    format: string;
    
    version: string;
    
    creator: string;
    
    createdAt: Date;
    
    updatedAt: Date;
    
    license: string;
    
    thumbnail?: string;
    
    technicalSpecs: {
        polygonCount?: number;
        textureResolution?: string;
        animationDuration?: number;
        fileFormat: string;
    };
    
    usageStats: {
        downloadCount: number;
        usageCount: number;
        rating: number;
        reviewCount: number;
    };
}

export interface AssetSearchCriteria {
    
    keywords?: string;
    
    type?: string;
    
    category?: string;
    
    tags?: string[];
    
    creator?: string;
    
    license?: string;
    
    format?: string;
    
    sortBy?: 'name' | 'createdAt' | 'rating' | 'usageCount';
    
    sortOrder?: 'asc' | 'desc';
    
    page?: number;
    
    pageSize?: number;
}

export interface AssetUploadConfig {
    
    allowedTypes: string[];
    
    maxFileSize: number;
    
    generateThumbnail: boolean;
    
    extractMetadata: boolean;
    
    storagePath: string;
}

export interface CacheConfig {
    
    type: 'redis' | 'memory';
    
    redis?: {
        host: string;
        port: number;
        password?: string;
        db?: number;
        keyPrefix?: string;
    };
    
    memory?: {
        maxSize: number;
        ttl: number;
    };
    
    enableCompression?: boolean;
    
    enableSerialization?: boolean;
}

export interface CacheStats {
    
    totalKeys: number;
    
    hits: number;
    
    misses: number;
    
    hitRate: number;
    
    memoryUsage: number;
    
    expiredKeys: number;
}

export interface TemplateSearchCriteria {
    
    keywords?: string;
    
    type?: string;
    
    style?: string;
    
    tags?: string[];
    
    creator?: string;
    
    publicOnly?: boolean;
    
    sortBy?: 'name' | 'createdAt' | 'rating' | 'usageCount';
    
    sortOrder?: 'asc' | 'desc';
    
    page?: number;
    
    pageSize?: number;
}

export interface TemplateApplyOptions {
    
    parameters?: Record<string, any>;
    
    preserveExisting?: boolean;
    
    autoScale?: boolean;
    
    targetBounds?: any;
}

export interface ServiceConfig {
    
    cache?: {
        type: 'redis' | 'memory';
        redis?: any;
        memory?: any;
    };
    
    database?: {
        type: 'mongodb' | 'postgresql';
        url: string;
        options?: any;
    };
    
    storage?: {
        type: 'local' | 's3' | 'minio';
        config: any;
    };
    
    ai?: {
        sceneGeneration?: any;
        voiceProcessing?: any;
        performance?: any;
    };
}

export interface ServiceStatus {
    
    name: string;
    
    initialized: boolean;
    
    healthy: boolean;
    
    lastCheckAt: Date;
    
    error?: string;
}

export interface UserPreferences {
    
    language: string;
    
    theme: 'light' | 'dark' | 'auto';
    
    sceneGeneration: {
        
        defaultStyle: string;
        
        defaultQuality: 'fast' | 'balanced' | 'high';
        
        enableRealTimePreview: boolean;
        
        enableVoiceGuidance: boolean;
    };
    
    ui: {
        
        showAdvancedOptions: boolean;
        
        autoSave: boolean;
        
        autoSaveInterval: number;
    };
    
    notifications: {
        
        email: boolean;
        
        browser: boolean;
        
        types: string[];
    };
}

export interface UserStats {
    
    sceneGenerationCount: number;
    
    voiceInteractionCount: number;
    
    templateUsageCount: number;
    
    assetDownloadCount: number;
    
    totalUsageTime: number;
    
    favoriteStyle?: string;
    
    averageSceneComplexity: number;
}

export interface User {
    
    id: string;
    
    username: string;
    
    email: string;
    
    displayName: string;
    
    avatar?: string;
    
    role: 'admin' | 'premium' | 'standard' | 'guest';
    
    status: 'active' | 'inactive' | 'suspended';
    
    preferences: UserPreferences;
    
    permissions: string[];
    
    createdAt: Date;
    
    lastLoginAt?: Date;
    
    stats: UserStats;
}

export interface LoginCredentials {
    
    identifier: string;
    
    password: string;
    
    rememberMe?: boolean;
}

export interface RegisterInfo {
    
    username: string;
    
    email: string;
    
    password: string;
    
    displayName: string;
    
    inviteCode?: string;
}

export interface MinIOConfig {
    
    endpoint: string;
    
    port?: number;
    
    useSSL?: boolean;
    
    accessKey: string;
    
    secretKey: string;
    
    defaultBucket?: string;
    
    region?: string;
}

export interface UploadOptions {
    
    bucket?: string;
    
    path?: string;
    
    contentType?: string;
    
    metadata?: Record<string, string>;
    
    overwrite?: boolean;
    
    onProgress?: (progress: number) => void;
}

export interface FileInfo {
    
    id: string;
    
    name: string;
    
    size: number;
    
    lastModified: Date;
    
    contentType: string;
    
    etag: string;
    
    metadata: Record<string, string>;
    
    fullPath: string;
    
    url?: string;
}

export interface DigitalHumanStoragePaths {
    
    userRoot: string;
    
    digitalHumanRoot: string;
    
    sourcePhotos: string;
    
    models: string;
    
    textures: string;
    
    animations: string;
    
    bipFiles: string;
    
    clothing: string;
    
    thumbnails: string;
}

export interface SurgicalTool {
    
    id: string;
    
    name: string;
    
    type: SurgicalToolType;
    
    available: boolean;
    
    entity: Entity;
}

export interface VesselInfo {
    
    donor: any;
    
    patient: any;
}

export interface TerrainTextureLayer {
    
    texture: string | THREE.Texture;
    
    normalMap?: string | THREE.Texture;
    
    roughnessMap?: string | THREE.Texture;
    
    displacementMap?: string | THREE.Texture;
    
    aoMap?: string | THREE.Texture;
    
    tiling: number;
    
    weight?: number;
    
    minHeight?: number;
    
    maxHeight?: number;
    
    minSlope?: number;
    
    maxSlope?: number;
}

export interface TerrainComponentOptions {
    
    width?: number;
    
    height?: number;
    
    resolution?: number;
    
    maxHeight?: number;
    
    heightMap?: string | THREE.Texture | Float32Array;
    
    layers?: TerrainTextureLayer[];
    
    useLOD?: boolean;
    
    lodLevels?: number;
    
    lodDistances?: number[];
    
    usePhysics?: boolean;
    
    physicsResolution?: number;
}

export interface BenchmarkConfig {
    
    enabled?: boolean;
    
    testDuration?: number;
    
    warmupDuration?: number;
    
    sampleInterval?: number;
    
    cameraPathType?: CameraPathType;
    
    testLOD?: boolean;
    
    testFrustumCulling?: boolean;
    
    testTextureStreaming?: boolean;
    
    testVirtualTexturing?: boolean;
    
    testGeometryCompression?: boolean;
    
    testPhysicsLOD?: boolean;
    
    debug?: boolean;
}

export interface BenchmarkResult {
    
    name: string;
    
    duration: number;
    
    averageFPS: number;
    
    minFPS: number;
    
    maxFPS: number;
    
    fpsStdDev: number;
    
    averageFrameTime: number;
    
    averageCPUUsage: number;
    
    averageGPUUsage: number;
    
    averageMemoryUsage: number;
    
    averageRenderTime: number;
    
    averagePhysicsTime: number;
    
    averageVisibleTerrainChunks: number;
    
    averageTerrainTriangles: number;
    
    averageTerrainVertices: number;
    
    averageTerrainTextureMemory: number;
    
    averageTerrainGeometryMemory: number;
    
    averageTerrainPhysicsMemory: number;
    
    samples: PerformanceData[];
    
    config: BenchmarkConfig;
    
    timestamp: number;
}

export interface PerformanceMonitorConfig {
    
    enabled?: boolean;
    
    updateFrequency?: number;
    
    sampleSize?: number;
    
    recordHistory?: boolean;
    
    maxHistoryLength?: number;
    
    enableWarnings?: boolean;
    
    enableBottleneckDetection?: boolean;
    
    warningThresholds?: {
        
        minFps?: number;
        
        maxFrameTime?: number;
        
        maxCpuUsage?: number;
        
        maxGpuUsage?: number;
        
        maxMemoryUsage?: number;
        
        maxRenderTime?: number;
        
        maxPhysicsTime?: number;
    };
}

export interface BatchExportResult {
    
    fileName: string;
    
    data: Blob | string;
    
    success: boolean;
    
    error?: string;
}

export interface HeightMapExportOptions {
    
    format: HeightMapFormat;
    
    width?: number;
    
    height?: number;
    
    flipY?: boolean;
    
    normalize?: boolean;
    
    bitDepth?: number;
    
    metadata?: Record<string, any>;
}

export interface HeightMapImportOptions {
    
    flipY?: boolean;
    
    heightScale?: number;
    
    applySmoothing?: boolean;
    
    smoothingStrength?: number;
    
    metadata?: Record<string, any>;
}

export interface TerrainExportOptions {
    
    includeTextures?: boolean;
    
    includeNormals?: boolean;
    
    includePhysics?: boolean;
    
    prettyPrint?: boolean;
    
    metadata?: Record<string, any>;
}

export interface TerrainImportOptions {
    
    keepTextures?: boolean;
    
    keepPhysics?: boolean;
    
    computeNormals?: boolean;
    
    applySmoothing?: boolean;
    
    smoothingStrength?: number;
    
    heightScale?: number;
    
    metadata?: Record<string, any>;
}

export interface TerrainData {
    
    width: number;
    
    height: number;
    
    resolution: number;
    
    maxHeight: number;
    
    heightData: Float32Array;
    
    layers?: any[];
    
    physics?: any;
    
    metadata?: Record<string, any>;
}

export interface ThirdPartyTerrainExportOptions {
    
    format: ThirdPartyTerrainFormat;
    
    includeTextures?: boolean;
    
    includeNormals?: boolean;
    
    includePhysics?: boolean;
    
    heightScale?: number;
    
    heightOffset?: number;
    
    sourceCoordinateSystem?: CoordinateSystem;
    
    targetCoordinateSystem?: CoordinateSystem;
    
    flipX?: boolean;
    
    flipY?: boolean;
    
    flipZ?: boolean;
    
    width?: number;
    
    height?: number;
    
    metadata?: Record<string, any>;
}

export interface ThirdPartyTerrainImportOptions {
    
    format: ThirdPartyTerrainFormat;
    
    keepTextures?: boolean;
    
    keepPhysics?: boolean;
    
    computeNormals?: boolean;
    
    applySmoothing?: boolean;
    
    smoothingStrength?: number;
    
    heightScale?: number;
    
    heightOffset?: number;
    
    sourceCoordinateSystem?: CoordinateSystem;
    
    targetCoordinateSystem?: CoordinateSystem;
    
    flipX?: boolean;
    
    flipY?: boolean;
    
    flipZ?: boolean;
    
    metadata?: Record<string, any>;
}

export interface OptimizedTerrainMaterialOptions {
    
    layers?: TerrainTextureLayer[];
    
    maxHeight?: number;
    
    useLOD?: boolean;
    
    lodDistance?: number;
    
    lodFactor?: number;
    
    useFog?: boolean;
    
    fogColor?: THREE.Color;
    
    fogNear?: number;
    
    fogFar?: number;
    
    ambientColor?: THREE.Color;
    
    lightColor?: THREE.Color;
    
    lightPosition?: THREE.Vector3;
    
    useClipPlane?: boolean;
    
    clipPlane?: THREE.Vector4;
    
    useBlendMaps?: boolean;
    
    wireframe?: boolean;
}

export interface TerrainMaterialOptions {
    
    layers: TerrainTextureLayer[];
    
    resolution: number;
    
    width: number;
    
    height: number;
    
    maxHeight: number;
}

export interface TerrainCDLODOptions {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    useFrustumCulling?: boolean;
    
    maxQuadTreeDepth?: number;
    
    baseChunkSize?: number;
    
    morphRegionRatio?: number;
    
    lodDistanceFactor?: number;
    
    useGPUMorphing?: boolean;
    
    useDebugVisualization?: boolean;
}

export interface TerrainChunk {
    
    id: string;
    
    indexX: number;
    
    indexZ: number;
    
    size: number;
    
    resolution: number;
    
    center: THREE.Vector3;
    
    boundingBox: THREE.Box3;
    
    boundingSphere: THREE.Sphere;
    
    lodLevel: number;
    
    mesh: THREE.Mesh | null;
    
    geometry: THREE.BufferGeometry | null;
    
    material: THREE.Material | null;
    
    heightData: Float32Array;
    
    normalData: Float32Array;
    
    visible: boolean;
    
    loaded: boolean;
    
    needsUpdate: boolean;
}

export interface QuadTreeNode {
    
    id: string;
    
    level: number;
    
    bounds: THREE.Box2;
    
    center: THREE.Vector2;
    
    size: number;
    
    children: QuadTreeNode[] | null;
    
    visible: boolean;
    
    inMorphRegion: boolean;
    
    morphFactor: number;
    
    chunk: TerrainChunk | null;
}

export interface GeometryCompressionConfig {
    
    enabled?: boolean;
    
    useQuantization?: boolean;
    
    positionQuantizationBits?: number;
    
    normalQuantizationBits?: number;
    
    uvQuantizationBits?: number;
    
    useIndexOptimization?: boolean;
    
    useVertexCacheOptimization?: boolean;
    
    useVertexFetchOptimization?: boolean;
    
    useMeshSimplification?: boolean;
    
    simplificationQuality?: number;
    
    useGPUAcceleration?: boolean;
    
    debug?: boolean;
}

export interface NormalMapOptimizationConfig {
    
    enabled?: boolean;
    
    useCompression?: boolean;
    
    compressionQuality?: number;
    
    useNormalMapMerging?: boolean;
    
    useNormalMapScaling?: boolean;
    
    maxTextureSize?: number;
    
    useNormalMapGeneration?: boolean;
    
    normalMapGenerationStrength?: number;
    
    useGPUAcceleration?: boolean;
    
    debug?: boolean;
}

export interface OptimizationResult {
    
    originalTexture: THREE.Texture;
    
    optimizedTexture: THREE.Texture;
    
    originalSize: number;
    
    optimizedSize: number;
    
    optimizationRatio: number;
    
    appliedOptimizations: string[];
    
    processingTime: number;
}

export interface TerrainPhysicsLODConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    highLevelDistance?: number;
    
    mediumLevelDistance?: number;
    
    lowLevelDistance?: number;
    
    veryLowLevelDistance?: number;
    
    highLevelResolution?: number;
    
    mediumLevelResolution?: number;
    
    lowLevelResolution?: number;
    
    veryLowLevelResolution?: number;
    
    useDebugVisualization?: boolean;
}

export interface PreviewRendererOptions {
    
    width: number;
    
    height: number;
    
    backgroundColor?: number;
    
    useAO?: boolean;
    
    useShadows?: boolean;
    
    useWireframe?: boolean;
    
    useGrid?: boolean;
}

export interface UndergroundWaterRenderParams {
    
    type: UndergroundWaterType;
    
    color: THREE.Color;
    
    opacity: number;
    
    reflectivity: number;
    
    refractionRatio: number;
    
    waveSpeed: number;
    
    waveStrength: number;
    
    depth: number;
    
    enableLOD: boolean;
    
    lodDistance: number[];
    
    enableFrustumCulling: boolean;
    
    enableOcclusionCulling: boolean;
    
    enableInstancing: boolean;
}

export interface TerrainChunkSystemOptions {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    chunkSize?: number;
    
    chunkResolution?: number;
    
    useOctree?: boolean;
    
    useFrustumCulling?: boolean;
    
    useLOD?: boolean;
    
    lodDistances?: number[];
    
    useGPUInstancing?: boolean;
    
    useDebugVisualization?: boolean;
}

export interface TerrainInstanceData {
    
    position: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    scale: THREE.Vector3;
    
    color: THREE.Color;
    
    visible: boolean;
    
    userData: any;
}

export interface TerrainInstanceGroup {
    
    id: string;
    
    geometry: THREE.BufferGeometry;
    
    material: THREE.Material;
    
    instancedMesh: THREE.InstancedMesh;
    
    instances: TerrainInstanceData[];
    
    availableIndices: number[];
    
    instanceIdToIndex: Map<string, number>;
    
    visible: boolean;
    
    needsUpdate: boolean;
    
    userData: any;
}

export interface TerrainInstancedRenderingSystemOptions {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    maxBatchSize?: number;
    
    useFrustumCulling?: boolean;
    
    useOctree?: boolean;
    
    useInstanceLOD?: boolean;
    
    useInstanceShadow?: boolean;
    
    useDebugVisualization?: boolean;
}

export interface TextureCompressionOptions {
    
    format?: TextureCompressionFormat;
    
    quality?: number;
    
    generateMipmaps?: boolean;
    
    useGPUCompression?: boolean;
    
    astcBlockSize?: '4x4' | '5x5' | '6x6' | '8x8' | '10x10' | '12x12';
    
    sRGB?: boolean;
    
    maxTextureSize?: number;
    
    keepOriginal?: boolean;
}

export interface TextureStreamingOptions {
    
    maxConcurrentLoads?: number;
    
    maxMemoryUsage?: number;
    
    useCompression?: boolean;
    
    compressionOptions?: TextureCompressionOptions;
    
    useLowResPlaceholders?: boolean;
    
    lowResTextureSize?: number;
    
    usePredictiveLoading?: boolean;
    
    autoUnload?: boolean;
    
    unloadCheckInterval?: number;
    
    textureUnusedTime?: number;
    
    debug?: boolean;
}

export interface VirtualTextureConfig {
    
    enabled?: boolean;
    
    physicalTextureSize?: number;
    
    pageSize?: number;
    
    pageBorderSize?: number;
    
    maxMemoryUsage?: number;
    
    maxPageCount?: number;
    
    maxMipLevels?: number;
    
    useCompression?: boolean;
    
    compressionOptions?: TextureCompressionOptions;
    
    useGPUAcceleration?: boolean;
    
    usePredictiveLoading?: boolean;
    
    useDebugVisualization?: boolean;
    
    debug?: boolean;
}

export interface VirtualTexturePage {
    
    id: string;
    
    mipLevel: number;
    
    x: number;
    
    y: number;
    
    texture: THREE.Texture | null;
    
    loaded: boolean;
    
    lastUsedTime: number;
    
    priority: number;
    
    size: number;
}

export interface TerrainVirtualTexturingSystemConfig extends VirtualTextureConfig {
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
}

export interface CaveSystemParams {
    
    count: number;
    
    minRadius: number;
    
    maxRadius: number;
    
    depth: number;
    
    complexity: number;
    
    connectionProbability: number;
    
    stalactiteProbability: number;
    
    stalagmiteProbability: number;
    
    minDepthRatio: number;
    
    maxDepthRatio: number;
    
    seed: number;
}

export interface CaveNode {
    
    x: number;
    
    y: number;
    
    z: number;
    
    radius: number;
}

export interface CaveConnection {
    
    fromIndex: number;
    
    toIndex: number;
    
    radius: number;
}

export interface CaveFormation {
    
    type: 'stalactite' | 'stalagmite';
    
    x: number;
    
    y: number;
    
    z: number;
    
    height: number;
    
    baseRadius: number;
    
    tipRadius: number;
}

export interface CaveSystem {
    
    nodes: CaveNode[];
    
    connections: CaveConnection[];
    
    formations: CaveFormation[];
}

export interface ThermalErosionParams {
    
    iterations: number;
    
    strength: number;
    
    slopeThreshold: number;
    
    depositionRate: number;
}

export interface HydraulicErosionParams {
    
    iterations: number;
    
    droplets: number;
    
    capacity: number;
    
    erosionStrength: number;
    
    depositionStrength: number;
    
    evaporationRate: number;
    
    inertia: number;
    
    minSlope: number;
    
    gravity: number;
}

export interface RiverGenerationParams {
    
    count: number;
    
    width: number;
    
    depth: number;
    
    sinuosity: number;
    
    branchProbability: number;
    
    minLength: number;
    
    maxLength: number;
    
    seed: number;
}

export interface MountainGenerationParams {
    
    count: number;
    
    height: number;
    
    width: number;
    
    roughness: number;
    
    sharpness: number;
    
    seed: number;
}

export interface CanyonGenerationParams {
    
    count: number;
    
    depth: number;
    
    width: number;
    
    sinuosity: number;
    
    roughness: number;
    
    seed: number;
}

export interface CaveGenerationParams {
    
    count: number;
    
    size: number;
    
    depth: number;
    
    complexity: number;
    
    connectionProbability: number;
    
    seed: number;
}

export interface CliffGenerationParams {
    
    count: number;
    
    height: number;
    
    width: number;
    
    steepness: number;
    
    roughness: number;
    
    seed: number;
}

export interface VolcanoGenerationParams {
    
    count: number;
    
    height: number;
    
    radius: number;
    
    craterSize: number;
    
    craterDepth: number;
    
    seed: number;
}

export interface TerrainFeatureCombinationParams {
    
    baseTerrainType: TerrainFeatureType;
    
    baseTerrainParams: any;
    
    features: {
        
        type: TerrainFeatureType;
        
        params: any;
        
        weight: number;
    }[];
    
    seed: number;
}

export interface TerrainPhysicsMaterialProps {
    
    friction: number;
    
    restitution: number;
    
    density: number;
}

export interface BrushParams {
    
    type: BrushType;
    
    shape: BrushShape;
    
    size: number;
    
    strength: number;
    
    falloff: number;
    
    targetHeight?: number;
    
    noiseSeed?: number;
    
    noiseScale?: number;
    
    textureIndex?: number;
}

export interface TerrainGenerationParams {
    
    seed: number;
    
    scale: number;
    
    persistence: number;
    
    octaves: number;
    
    frequency: number;
    
    amplitude: number;
    
    erosionIterations: number;
    
    erosionStrength: number;
}

export interface UndergroundWaterfall {
    
    startX: number;
    
    startZ: number;
    
    endX: number;
    
    endZ: number;
    
    height: number;
    
    width: number;
    
    flowRate: number;
}

export interface HotSpring {
    
    x: number;
    
    z: number;
    
    radius: number;
    
    temperature: number;
    
    bubbleRate: number;
}

export interface Stalactite {
    
    x: number;
    
    z: number;
    
    height: number;
    
    radius: number;
    
    type: 'stalactite' | 'stalagmite';
}

export interface UndergroundLakeParams {
    
    count: number;
    
    minRadius: number;
    
    maxRadius: number;
    
    depth: number;
    
    complexity: number;
    
    depthVariation: number;
    
    caveProbability: number;
    
    riverProbability: number;
    
    minDepthRatio: number;
    
    maxDepthRatio: number;
    
    shapeType?: LakeShapeType;
    
    generateStalactites?: boolean;
    
    stalactiteDensity?: number;
    
    generateWaterfall?: boolean;
    
    waterfallHeight?: number;
    
    generateHotSpring?: boolean;
    
    hotSpringTemperature?: number;
    
    seed: number;
}

export interface UndergroundLakeNode {
    
    x: number;
    
    z: number;
    
    radius: number;
    
    depthRatio: number;
    
    controlPoints?: {
        x: number;
        z: number;
        influence: number;
    }[];
    
    type?: 'main' | 'branch' | 'connection';
}

export interface UndergroundLake {
    
    nodes: UndergroundLakeNode[];
    
    connectedCaves: any[];
    
    connectedRivers: any[];
    
    shapeType?: LakeShapeType;
    
    waterfalls?: UndergroundWaterfall[];
    
    hotSprings?: HotSpring[];
    
    stalactites?: Stalactite[];
}

export interface UndergroundRiverParams {
    
    count: number;
    
    width: number;
    
    depth: number;
    
    sinuosity: number;
    
    branchProbability: number;
    
    minLength: number;
    
    maxLength: number;
    
    minDepthRatio: number;
    
    maxDepthRatio: number;
    
    caveProbability: number;
    
    seed: number;
}

export interface TerrainWorkerMessageData {
    
    type: TerrainWorkerMessageType;
    
    data: any;
}

export interface TerrainGenerationWorkerParams {
    
    resolution: number;
    
    algorithm: string;
    
    params: any;
    
    heightData?: Float32Array;
}

export interface ThermalErosionWorkerParams {
    
    iterations: number;
    
    strength: number;
    
    slopeThreshold: number;
    
    depositionRate: number;
}

export interface HydraulicErosionWorkerParams {
    
    iterations: number;
    
    droplets: number;
    
    capacity: number;
    
    erosionStrength: number;
    
    depositionStrength: number;
    
    evaporationRate: number;
    
    inertia: number;
    
    minSlope: number;
    
    gravity: number;
}

export interface TerrainFeatureCombinationWorkerParams {
    
    baseTerrainType: TerrainFeatureType;
    
    baseTerrainParams: any;
    
    features: {
        
        type: TerrainFeatureType;
        
        params: any;
        
        weight: number;
    }[];
    
    seed: number;
}

export interface TerrainGenerationWorkerManagerConfig {
    
    maxWorkers?: number;
    
    enableMultithreading?: boolean;
    
    debug?: boolean;
}

export interface ClothingEditorConfig {
    
    container: HTMLElement;
    
    showPreview: boolean;
    
    enableDragDrop: boolean;
    
    theme: 'light' | 'dark';
    
    language: 'zh' | 'en';
}

export interface BIPUploadPanelConfig {
    
    container: HTMLElement;
    
    allowMultiple?: boolean;
    
    maxFileSize?: number;
    
    showPreview?: boolean;
}

export interface UploadItem {
    
    file: File;
    
    status: UploadStatus;
    
    progress: number;
    
    result?: BIPImportResult;
    
    error?: string;
}

export interface DigitalHumanUploadPanelConfig {
    
    container: HTMLElement;
    
    supportedFormats?: SupportedFileFormat[];
    
    maxFileSize?: number;
    
    showPreview?: boolean;
    
    allowBatch?: boolean;
}

export interface MultiActionPanelConfig {
    
    container: HTMLElement;
    
    showDebugInfo?: boolean;
    
    maxDisplayActions?: number;
    
    allowDragSort?: boolean;
}

export interface ActionItemState {
    
    selected: boolean;
    
    playing: boolean;
    
    hasConflict: boolean;
    
    enabled: boolean;
}

export interface UIComponentProps {
    id?: string;
    type?: UIComponentType;
    visible?: boolean;
    interactive?: boolean;
    position?: Vector3 | Vector2;
    size?: Vector2;
    opacity?: number;
    zIndex?: number;
    layoutType?: UILayoutType;
    layoutParams?: any;
    backgroundColor?: string;
    borderColor?: string;
    borderWidth?: number;
    borderRadius?: number;
    padding?: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
    margin?: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
    onClick?: (event: any) => void;
    onHover?: (event: any) => void;
    onDragStart?: (event: any) => void;
    onDrag?: (event: any) => void;
    onDragEnd?: (event: any) => void;
    data?: any;
    tags?: string[];
    is3D?: boolean;
}

export interface UI2DComponentProps extends UIComponentProps {
    htmlElement?: HTMLElement;
    cssClass?: string;
    cssStyle?: Partial<CSSStyleDeclaration>;
    innerHTML?: string;
    textContent?: string;
    fontSize?: number | string;
    fontFamily?: string;
    fontWeight?: string | number;
    textAlign?: 'left' | 'center' | 'right' | 'justify';
    textColor?: string;
    lineHeight?: number | string;
    onFocus?: (event: any) => void;
    onBlur?: (event: any) => void;
    onKeyDown?: (event: any) => void;
    onKeyUp?: (event: any) => void;
    onChange?: (event: any) => void;
}

export interface UI3DComponentProps extends UIComponentProps {
    mesh?: Mesh;
    texture?: Texture;
    canvas?: HTMLCanvasElement;
    group?: Group;
    rotation?: Vector3 | Euler;
    scale?: Vector3;
    lookAt?: Vector3;
    billboardMode?: BillboardMode;
    transparent?: boolean;
    opacity?: number;
    color?: string;
    emissive?: string;
    emissiveIntensity?: number;
    textContent?: string;
    fontSize?: number;
    fontFamily?: string;
    fontColor?: string;
    textAlign?: 'left' | 'center' | 'right';
    textBaseline?: 'top' | 'middle' | 'bottom';
    interactionDistance?: number;
    hoverColor?: string;
    activeColor?: string;
}

export interface UIEventData {
    type: UIEventType;
    target: UIComponent;
    originalEvent?: any;
    position?: Vector2 | Vector3;
    delta?: Vector2;
    button?: number;
    key?: string;
    keyCode?: number;
    altKey?: boolean;
    ctrlKey?: boolean;
    shiftKey?: boolean;
    metaKey?: boolean;
    value?: any;
    stopPropagation: boolean;
    preventDefault: boolean;
    timestamp: number;
}

export interface GridLayoutParams {
    columns: number;
    rows?: number;
    cellWidth: number;
    cellHeight: number;
    columnGap?: number;
    rowGap?: number;
    justifyItems?: 'start' | 'center' | 'end' | 'stretch';
    alignItems?: 'start' | 'center' | 'end' | 'stretch';
    autoFlow?: 'row' | 'column';
}

export interface FlexLayoutParams {
    direction: 'row' | 'column' | 'row-reverse' | 'column-reverse';
    wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
    justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
    alignItems?: 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch';
    alignContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'stretch';
    gap?: number;
}

export interface AbsoluteLayoutParams {
}

export interface RelativeLayoutParams {
    spacing?: number;
    padding?: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
}

export interface LayoutItemParams {
    gridColumn?: string;
    gridRow?: string;
    gridArea?: string;
    flexGrow?: number;
    flexShrink?: number;
    flexBasis?: number | string;
    alignSelf?: 'auto' | 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch';
    order?: number;
    left?: number;
    top?: number;
    right?: number;
    bottom?: number;
    zIndex?: number;
    margin?: number | {
        top?: number;
        right?: number;
        bottom?: number;
        left?: number;
    };
}

export interface DigitalHumanEditorConfig {
    
    container: HTMLElement;
    
    debug?: boolean;
    
    storageConfig?: any;
    
    showToolbar?: boolean;
    
    showStatusBar?: boolean;
}

export interface IUIElement {
    
    id: string;
    
    entity: Entity;
    
    parent?: IUIElement;
    
    children: IUIElement[];
    
    visible: boolean;
    
    interactive: boolean;
    
    position: Vector3 | Vector2;
    
    size: Vector2;
    
    opacity: number;
    
    addChild(child: IUIElement): void;
    
    removeChild(child: IUIElement): void;
    
    update(deltaTime: number): void;
    
    render(): void;
    
    dispose(): void;
}

export interface IUILayout {
    
    type: UILayoutType;
    
    params: any;
    
    apply(element: IUIElement): void;
}

export interface IUIEvent {
    
    type: UIEventType;
    
    target: IUIElement;
    
    data: any;
    
    stopPropagation(): void;
    
    preventDefault(): void;
}

export interface IUIAnimation {
    
    type: UIAnimationType;
    
    target: IUIElement;
    
    duration: number;
    
    delay: number;
    
    easing: UIEasingFunction;
    
    loop: boolean;
    
    from: any;
    
    to: any;
    
    progress: number;
    
    start(): void;
    
    pause(): void;
    
    resume(): void;
    
    stop(): void;
    
    update(deltaTime: number): void;
    
    onComplete?: () => void;
}

export interface UI2DSystemConfig {
    debug?: boolean;
    autoCreateContainer?: boolean;
    containerId?: string;
    defaultFont?: string;
    defaultFontSize?: number;
    defaultTextColor?: string;
    defaultBackgroundColor?: string;
    defaultBorderColor?: string;
    defaultBorderWidth?: number;
    defaultBorderRadius?: number;
    defaultPadding?: number;
    defaultMargin?: number;
}

export interface UI3DSystemConfig {
    debug?: boolean;
    defaultFont?: string;
    defaultFontSize?: number;
    defaultFontColor?: string;
    defaultBackgroundColor?: string;
    defaultBorderColor?: string;
    defaultBorderWidth?: number;
    defaultBorderRadius?: number;
    defaultPadding?: number;
    defaultInteractionDistance?: number;
    defaultHoverColor?: string;
    defaultActiveColor?: string;
    defaultBillboardMode?: BillboardMode;
}

export interface UIAnimationSystemConfig {
    debug?: boolean;
    defaultDuration?: number;
    defaultDelay?: number;
    defaultEasing?: (t: number) => number;
}

export interface UILayoutSystemConfig {
    debug?: boolean;
    autoApplyLayout?: boolean;
    autoApplyInterval?: number;
}

export interface UISystemConfig {
    debug?: boolean;
    autoCreateContainer?: boolean;
    containerId?: string;
    enableEvents?: boolean;
    enableLayouts?: boolean;
    enableAnimations?: boolean;
}

export interface DeviceCapabilitiesOptions {
    
    forceLowPerformance?: boolean;
    
    forceHighPerformance?: boolean;
    
    enableBatteryMonitoring?: boolean;
    
    enableTemperatureMonitoring?: boolean;
    
    enableNetworkMonitoring?: boolean;
    
    enablePerformanceMonitoring?: boolean;
    
    enableAutoPerformanceAdjustment?: boolean;
    
    lowBatteryThreshold?: number;
    
    highTemperatureThreshold?: number;
    
    targetFPS?: number;
    
    minAcceptableFPS?: number;
}

export interface GPUPerformanceAnalyzerConfig {
    
    enabled?: boolean;
    
    sampleInterval?: number;
    
    historyLength?: number;
    
    autoSample?: boolean;
    
    detectBottlenecks?: boolean;
    
    generateSuggestions?: boolean;
    
    debug?: boolean;
}

export interface GPUPerformanceMetrics {
    
    fps: number;
    
    drawCalls: number;
    
    triangles: number;
    
    points: number;
    
    lines: number;
    
    geometries: number;
    
    textures: number;
    
    programs: number;
    
    renderTargets: number;
    
    renderTime: number;
    
    postProcessingTime: number;
    
    memoryUsage: number;
    
    textureMemory: number;
    
    geometryMemory: number;
    
    timestamp: number;
}

export interface BottleneckInfo {
    
    type: BottleneckType;
    
    severity: number;
    
    description: string;
    
    metrics: Record<string, number>;
    
    timestamp: number;
}

export interface AnalysisReport {
    
    currentMetrics: GPUPerformanceMetrics;
    
    averageMetrics: GPUPerformanceMetrics;
    
    minMetrics: GPUPerformanceMetrics;
    
    maxMetrics: GPUPerformanceMetrics;
    
    metricsHistory: GPUPerformanceMetrics[];
    
    detectedBottlenecks: BottleneckInfo[];
    
    optimizationSuggestions: OptimizationSuggestion[];
    
    performanceScore: number;
    
    timestamp: number;
}

export interface MemorySnapshot {
    
    id: string;
    
    timestamp: number;
    
    totalMemory: number;
    
    jsHeapMemory: number;
    
    resourceMemory: number;
    
    memoryByType: Record<ResourceType, number>;
    
    resources: ResourceInfo[];
}

export interface MemoryAnalyzerConfig {
    
    enabled?: boolean;
    
    enableAutoSnapshot?: boolean;
    
    autoSnapshotInterval?: number;
    
    enableLeakDetection?: boolean;
    
    leakDetectionThreshold?: number;
    
    debug?: boolean;
    
    collectDetailedInfo?: boolean;
    
    enableWarnings?: boolean;
}

export interface TestScene {
    
    id: string;
    
    name: string;
    
    description: string;
    
    create: () => THREE.Scene;
    
    cleanup: () => void;
    
    update?: (deltaTime: number) => void;
    
    config?: Record<string, any>;
}

export interface TestResult {
    
    id: string;
    
    sceneId: string;
    
    techniqueId: string;
    
    report: AnalysisReport;
    
    averageFps: number;
    
    minFps: number;
    
    maxFps: number;
    
    averageRenderTime: number;
    
    averageMemoryUsage: number;
    
    averageDrawCalls: number;
    
    averageTriangles: number;
    
    duration: number;
    
    timestamp: number;
}

export interface OptimizationTechnique {
    
    id: string;
    
    name: string;
    
    description: string;
    
    apply: (scene: THREE.Scene, renderer: THREE.WebGLRenderer, config?: Record<string, any>) => void;
    
    cleanup: (scene: THREE.Scene, renderer: THREE.WebGLRenderer) => void;
    
    config?: Record<string, any>;
}

export interface ComparisonResult {
    
    baselineId: string;
    
    comparisonId: string;
    
    fpsImprovement: number;
    
    renderTimeImprovement: number;
    
    memoryUsageImprovement: number;
    
    drawCallsImprovement: number;
    
    trianglesImprovement: number;
    
    overallImprovement: number;
    
    timestamp: number;
}

export interface PerformanceMetric {
    
    type: PerformanceMetricType;
    
    name: string;
    
    value: number;
    
    min: number;
    
    max: number;
    
    average: number;
    
    history: number[];
    
    historyLimit: number;
    
    unit?: string;
    
    threshold?: number;
    
    exceedsThreshold?: boolean;
}

export interface PerformanceBottleneck {
    
    type: PerformanceBottleneckType;
    
    severity: number;
    
    description: string;
    
    relatedMetrics: string[];
    
    optimizationSuggestions: string[];
}

export interface PerformanceTrend {
    
    type: PerformanceTrendType;
    
    metricType: PerformanceMetricType;
    
    changeRate: number;
    
    startTime: number;
    
    duration: number;
}

export interface PerformanceReport {
    
    timestamp: number;
    
    metrics: {
        [key: string]: PerformanceMetric;
    };
    
    bottlenecks: PerformanceBottleneck[];
    
    trends: PerformanceTrend[];
    
    overallScore: number;
    
    status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
    
    customData?: {
        [key: string]: any;
    };
}

export interface ResourceTrackerConfig {
    
    enabled?: boolean;
    
    debug?: boolean;
    
    autoTrackAll?: boolean;
    
    autoCalculateSize?: boolean;
}

export interface VegetationItemConfig {
    
    model: string;
    
    density: number;
    
    minScale: number;
    
    maxScale: number;
    
    minHeight: number;
    
    maxHeight: number;
    
    slopeMin: number;
    
    slopeMax: number;
    
    randomRotation?: boolean;
    
    randomOffset?: number;
    
    avoidWater?: boolean;
    
    colorVariation?: {
        enabled: boolean;
        hue: number;
        saturation: number;
        lightness: number;
    };
    
    seasonalEffect?: boolean;
    
    windEffect?: boolean;
    
    userData?: any;
}

export interface VegetationComponentOptions {
    
    terrainEntity: string;
    
    items: VegetationItemConfig[];
    
    autoGenerate?: boolean;
    
    seed?: number;
    
    useInstancing?: boolean;
    
    useLOD?: boolean;
    
    lodDistances?: number[];
    
    useFrustumCulling?: boolean;
    
    useOctree?: boolean;
    
    useGPUInstancing?: boolean;
    
    useShadow?: boolean;
    
    useWind?: boolean;
    
    windParams?: {
        strength: number;
        direction: THREE.Vector2;
        frequency: number;
        turbulence: number;
    };
    
    useSeasonal?: boolean;
    
    seasonalParams?: {
        season: 'spring' | 'summer' | 'autumn' | 'winter';
        intensity: number;
    };
    
    useDistributionMap?: boolean;
    
    distributionMap?: string | THREE.Texture;
    
    useDensityMap?: boolean;
    
    densityMap?: string | THREE.Texture;
}

export interface VegetationInstance {
    
    id: string;
    
    itemIndex: number;
    
    position: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    scale: THREE.Vector3;
    
    color: THREE.Color;
    
    visible: boolean;
    
    lodLevel: number;
    
    userData: any;
}

export interface EcosystemSimulationSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    useDebugVisualization?: boolean;
    
    useEcosystemGrid?: boolean;
    
    ecosystemGridResolution?: number;
    
    useSeasonalChanges?: boolean;
    
    useVegetationCompetition?: boolean;
    
    useVegetationGrowth?: boolean;
}

export interface VegetationData {
    
    type: VegetationType;
    
    growthStage: VegetationGrowthStage;
    
    age: number;
    
    health: number;
    
    competitiveness: number;
    
    droughtTolerance: number;
    
    coldTolerance: number;
    
    heatTolerance: number;
    
    wetTolerance: number;
    
    lightRequirement: number;
    
    nutrientRequirement: number;
    
    growthRate: number;
    
    maxAge: number;
    
    maxHeight: number;
    
    maxWidth: number;
    
    currentHeight: number;
    
    currentWidth: number;
    
    evergreen: boolean;
    
    flowering: boolean;
    
    floweringSeason: SeasonType[];
    
    fruiting: boolean;
    
    fruitingSeason: SeasonType[];
    
    deciduous: boolean;
    
    leafFallSeason: SeasonType[];
    
    userData?: any;
}

export interface EcosystemGridCell {
    
    position: THREE.Vector3;
    
    size: THREE.Vector3;
    
    vegetationData: VegetationData[];
    
    soilMoisture: number;
    
    soilFertility: number;
    
    lightIntensity: number;
    
    temperature: number;
    
    elevation: number;
    
    slope: number;
    
    aspect: number;
    
    isWater: boolean;
    
    userData?: any;
}

export interface VegetationInteractionRule {
    
    sourceType: VegetationType;
    
    targetType: VegetationType;
    
    interactionType: VegetationInteractionType;
    
    strength: number;
    
    distance: number;
    
    considerGrowthStage: boolean;
    
    sourceGrowthStage?: VegetationGrowthStage;
    
    targetGrowthStage?: VegetationGrowthStage;
    
    considerHeightDifference: boolean;
    
    considerWidthDifference: boolean;
    
    considerCompetitivenessDifference: boolean;
    
    considerHealthDifference: boolean;
    
    considerAgeDifference: boolean;
    
    userData?: any;
}

export interface VegetationInteractionResult {
    
    source: VegetationData;
    
    target: VegetationData;
    
    interactionType: VegetationInteractionType;
    
    strength: number;
    
    sourceHealthChange: number;
    
    targetHealthChange: number;
    
    sourceGrowthRateChange: number;
    
    targetGrowthRateChange: number;
    
    sourceCompetitivenessChange: number;
    
    targetCompetitivenessChange: number;
    
    userData?: any;
}

export interface VegetationGrowthSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    useDebugVisualization?: boolean;
    
    useSeasonalChanges?: boolean;
    
    useGrowthStages?: boolean;
    
    useAgeSystem?: boolean;
    
    useHealthSystem?: boolean;
    
    useEnvironmentInfluence?: boolean;
    
    growthSpeedMultiplier?: number;
}

export interface VegetationInstanceData {
    
    id: string;
    
    type: VegetationType;
    
    growthStage: VegetationGrowthStage;
    
    age: number;
    
    health: number;
    
    maxAge: number;
    
    growthRate: number;
    
    currentHeight: number;
    
    maxHeight: number;
    
    currentWidth: number;
    
    maxWidth: number;
    
    currentScale: THREE.Vector3;
    
    maxScale: THREE.Vector3;
    
    position: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    evergreen: boolean;
    
    flowering: boolean;
    
    floweringSeason: SeasonType[];
    
    fruiting: boolean;
    
    fruitingSeason: SeasonType[];
    
    deciduous: boolean;
    
    leafFallSeason: SeasonType[];
    
    userData: any;
}

export interface AquaticVegetationConfig {
    
    type: AquaticVegetationType;
    
    maxHeight?: number;
    
    maxWidth?: number;
    
    maxAge?: number;
    
    growthRate?: number;
    
    waterDepthRange?: {
        min: number;
        max: number;
    };
    
    waterFlowSpeedRange?: {
        min: number;
        max: number;
    };
    
    waterQualityRequirement?: number;
    
    lightRequirement?: number;
    
    temperatureRange?: {
        min: number;
        max: number;
    };
    
    isFloating?: boolean;
    
    isSubmerged?: boolean;
    
    isEmergent?: boolean;
    
    canFlower?: boolean;
    
    floweringSeason?: SeasonType[];
    
    canFruit?: boolean;
    
    fruitingSeason?: SeasonType[];
    
    isEvergreen?: boolean;
    
    userData?: any;
}

export interface SeasonalVegetationConfig {
    
    type: SeasonalVegetationType;
    
    maxHeight?: number;
    
    maxWidth?: number;
    
    maxAge?: number;
    
    growthRate?: number;
    
    activeSeasons?: SeasonType[];
    
    dormantSeasons?: SeasonType[];
    
    floweringSeason?: SeasonType[];
    
    fruitingSeason?: SeasonType[];
    
    leafFallSeason?: SeasonType[];
    
    isEvergreen?: boolean;
    
    temperatureRange?: {
        min: number;
        max: number;
    };
    
    lightRequirement?: number;
    
    moistureRequirement?: number;
    
    nutrientRequirement?: number;
    
    userData?: any;
}

export interface VegetationSystemOptions {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    useInstancing?: boolean;
    
    useLOD?: boolean;
    
    useFrustumCulling?: boolean;
    
    useOctree?: boolean;
    
    useGPUInstancing?: boolean;
    
    useShadow?: boolean;
    
    useWind?: boolean;
    
    useDebugVisualization?: boolean;
}

export interface WindZone {
    
    type: WindZoneType;
    
    position: THREE.Vector3;
    
    size: THREE.Vector3;
    
    rotation: THREE.Euler;
    
    fieldType: WindFieldType;
    
    strength: number;
    
    direction: THREE.Vector3;
    
    frequency: number;
    
    turbulence: number;
    
    falloffDistance: number;
    
    enabled: boolean;
    
    userData?: any;
}

export interface PhysicalWindSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    usePhysics?: boolean;
    
    useGPU?: boolean;
    
    useWindZones?: boolean;
    
    useDebugVisualization?: boolean;
    
    globalWind?: {
        
        fieldType?: WindFieldType;
        
        strength?: number;
        
        direction?: THREE.Vector3;
        
        frequency?: number;
        
        turbulence?: number;
    };
}

export interface WindFieldSystemConfig {
    
    enabled?: boolean;
    
    autoUpdate?: boolean;
    
    updateFrequency?: number;
    
    useDebugVisualization?: boolean;
    
    useWindFieldGrid?: boolean;
    
    windFieldGridResolution?: number;
    
    useWindFieldParticles?: boolean;
    
    windFieldParticlesCount?: number;
}

export interface Breakpoint {
    
    id: string;
    
    nodeId: string;
    
    graphId: string;
    
    type: BreakpointType;
    
    condition?: string;
    
    logMessage?: string;
    
    variableName?: string;
    
    variableCondition?: string;
    
    exceptionType?: string;
    
    counter?: number;
    
    countThreshold?: number;
    
    tags?: string[];
    
    description?: string;
    
    createdAt?: number;
    
    lastHitAt?: number;
    
    hitCount?: number;
    
    enabled: boolean;
}

export interface BreakpointHitInfo {
    
    breakpoint: Breakpoint;
    
    node: Node;
    
    graph: Graph;
}

export interface NodePerformanceData {
    
    nodeId: string;
    
    nodeType?: string;
    
    nodeLabel?: string;
    
    executionCount: number;
    
    totalExecutionTime: number;
    
    minExecutionTime: number;
    
    maxExecutionTime: number;
    
    averageExecutionTime: number;
    
    lastExecutionTime: number;
    
    lastExecutionTimestamp: number;
    
    executionTimeHistory: number[];
    
    isExecuting: boolean;
    
    executionStartTimestamp?: number;
}

export interface GraphPerformanceData {
    
    graphId: string;
    
    graphName?: string;
    
    executionCount: number;
    
    totalExecutionTime: number;
    
    minExecutionTime: number;
    
    maxExecutionTime: number;
    
    averageExecutionTime: number;
    
    lastExecutionTime: number;
    
    lastExecutionTimestamp: number;
    
    executionTimeHistory: number[];
    
    isExecuting: boolean;
    
    executionStartTimestamp?: number;
    
    nodePerformance: Record<string, NodePerformanceData>;
}

export interface PerformanceAnalyzerConfig {
    
    enabled?: boolean;
    
    maxHistoryLength?: number;
    
    recordNodeExecutionTime?: boolean;
    
    recordGraphExecutionTime?: boolean;
    
    autoClearOldData?: boolean;
    
    autoClearThreshold?: number;
}

export interface VariableWatch {
    
    name: string;
    
    graphId: string;
    
    enabled: boolean;
    
    condition?: string;
    
    type?: string;
    
    format?: string;
    
    tags?: string[];
    
    description?: string;
    
    createdAt?: number;
    
    lastUpdatedAt?: number;
    
    updateCount?: number;
    
    recordHistory?: boolean;
    
    notifyOnChange?: boolean;
    
    notifyOnCondition?: boolean;
}

export interface VariableChangeInfo {
    
    name: string;
    
    graphId: string;
    
    oldValue: any;
    
    newValue: any;
    
    timestamp: number;
    
    changeType?: 'create' | 'update' | 'delete';
    
    description?: string;
    
    nodeId?: string;
    
    source?: string;
}

export interface ExecutionPathItem {
    
    nodeId: string;
    
    graphId: string;
    
    timestamp: number;
}

export interface CustomEventOptions {
    
    id: string;
    
    name: string;
    
    parameterTypes?: string[];
    
    description?: string;
}

export interface ExecutionContextOptions {
    
    engine: VisualScriptEngine;
    
    entity: Entity;
    
    world: World;
}

export interface FiberStepResult {
    
    completed: boolean;
    
    pause: boolean;
    
    node?: Node;
    
    result?: any;
    
    error?: any;
}

export interface FiberOptions {
    
    engine: VisualScriptEngine;
    
    sourceNode: Node;
    
    outputName: string;
    
    callback?: () => void;
}

export interface GraphOptions {
    
    id: string;
    
    name?: string;
    
    description?: string;
}

export interface NodeMetadata {
    
    positionX: number;
    
    positionY: number;
    
    width?: number;
    
    height?: number;
    
    color?: string;
    
    label?: string;
    
    description?: string;
    
    collapsed?: boolean;
    
    locked?: boolean;
    
    visible?: boolean;
    
    zIndex?: number;
    
    [key: string]: any;
}

export interface NodeParameterValue {
    
    value?: any;
    
    link?: {
        
        nodeId: string;
        
        socket: string;
    };
}

export interface NodeParameters {
    
    [paramName: string]: NodeParameterValue;
}

export interface NodeFlowConnection {
    
    nodeId: string;
    
    socket: string;
}

export interface NodeFlows {
    
    [flowName: string]: NodeFlowConnection;
}

export interface NodeJSON {
    
    id: string;
    
    type: string;
    
    metadata: NodeMetadata;
    
    parameters?: NodeParameters;
    
    flows?: NodeFlows;
}

export interface VariableJSON {
    
    id: string;
    
    name: string;
    
    type: string;
    
    value: any;
    
    description?: string;
    
    constant?: boolean;
    
    global?: boolean;
}

export interface CustomEventJSON {
    
    id: string;
    
    name: string;
    
    parameterTypes?: string[];
    
    description?: string;
}

export interface GraphJSON {
    
    version?: string;
    
    name?: string;
    
    description?: string;
    
    nodes: NodeJSON[];
    
    variables: VariableJSON[];
    
    customEvents: CustomEventJSON[];
    
    metadata?: {
        
        createdAt?: string;
        
        updatedAt?: string;
        
        author?: string;
        
        tags?: string[];
        
        thumbnail?: string;
        
        [key: string]: any;
    };
}

export interface NodeDefinition {
    constructor: NodeConstructor;
    category: string;
    description: string;
    icon?: string;
    color?: string;
}

export interface NodeOptions {
    
    id: string;
    
    type: string;
    
    metadata?: NodeMetadata;
    
    graph: Graph;
    
    context: ExecutionContext;
}

export interface FlowNodeOptions extends NodeOptions {
    
    inputFlowName?: string;
    
    outputFlowNames?: string[];
}

export interface AsyncNodeOptions extends FlowNodeOptions {
    
    timeout?: number;
}

export interface EventNodeOptions extends NodeOptions {
    
    eventName?: string;
}

export interface FunctionNodeOptions extends NodeOptions {
    
    functionName?: string;
}

export interface SocketDefinition {
    
    name: string;
    
    type: SocketType;
    
    direction: SocketDirection;
    
    dataType?: string;
    
    defaultValue?: any;
    
    description?: string;
    
    optional?: boolean;
}

export interface Socket extends SocketDefinition {
    
    connectedNodeId?: string;
    
    connectedSocketName?: string;
    
    value?: any;
}

export interface NodeConnection {
    
    sourceNode: Node;
    
    sourceSocketName: string;
    
    targetNode: Node;
    
    targetSocketName: string;
}

export interface NodeTypeInfo {
    
    type: string;
    
    category: NodeCategory;
    
    constructor: NodeConstructor;
    
    description?: string;
    
    label?: string;
    
    icon?: string;
    
    color?: string;
    
    deprecated?: boolean;
    
    deprecatedReason?: string;
    
    experimental?: boolean;
    
    tags?: string[];
    
    examples?: string[];
    
    documentationUrl?: string;
    
    version?: string;
    
    author?: string;
    
    license?: string;
    
    dependencies?: string[];
    
    [key: string]: any;
}

export interface VisualScriptOptimizerConfig {
    
    cacheStrategy: CacheStrategy;
    
    maxCacheItems: number;
    
    cacheExpirationTime: number;
    
    enableLazyLoading: boolean;
    
    lazyLoadingViewRange: number;
    
    enableBatching: boolean;
    
    batchSize: number;
}

export interface ValueTypeInfo {
    
    type: string;
    
    label?: string;
    
    description?: string;
    
    icon?: string;
    
    color?: string;
    
    creator: ValueTypeCreator;
    
    validator: ValueTypeValidator;
    
    converter?: ValueTypeConverter;
    
    primitive?: boolean;
    
    composite?: boolean;
    
    custom?: boolean;
    
    deprecated?: boolean;
    
    deprecatedReason?: string;
    
    experimental?: boolean;
    
    tags?: string[];
    
    examples?: any[];
    
    documentationUrl?: string;
    
    version?: string;
    
    author?: string;
    
    license?: string;
    
    dependencies?: string[];
    
    [key: string]: any;
}

export interface VariableOptions {
    
    id: string;
    
    name: string;
    
    type: string;
    
    value?: any;
    
    description?: string;
    
    constant?: boolean;
    
    global?: boolean;
}

export interface VisualScriptComponentOptions {
    
    script?: GraphJSON;
    
    autoRun?: boolean;
    
    disabled?: boolean;
    
    domain?: string;
}

export interface VisualScriptEngineOptions {
    
    script: GraphJSON;
    
    nodeRegistry: NodeRegistry;
    
    valueTypeRegistry: ValueTypeRegistry;
    
    entity: Entity;
    
    world: World;
}

export interface NodeInput {
    name: string;
    type: string;
    label: string;
    defaultValue?: any;
}

export interface NodeOutput {
    name: string;
    type: string;
    label: string;
}

export interface VisualScriptContext {
    deltaTime?: number;
    time?: number;
    [key: string]: any;
}

export interface VisualScriptSystemOptions {
    
    autoInit?: boolean;
    
    defaultDomain?: string;
}

