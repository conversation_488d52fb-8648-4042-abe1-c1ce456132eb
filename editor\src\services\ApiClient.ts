/**
 * 统一API客户端
 * 提供与后端微服务的统一通信接口
 */
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { EventEmitter } from '../utils/EventEmitter';

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  enableCache: boolean;
  cacheTimeout: number;
  retryCount: number;
  retryDelay: number;
}

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  code?: number;
  success: boolean;
}

export interface RequestOptions extends AxiosRequestConfig {
  useCache?: boolean;
  skipAuth?: boolean;
  retryCount?: number;
}

/**
 * API客户端类
 */
export class ApiClient extends EventEmitter {
  private instance: AxiosInstance;
  private config: ApiConfig;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private authToken: string | null = null;

  constructor(config?: Partial<ApiConfig>) {
    super();

    // 默认配置
    this.config = {
      baseURL: this.getApiBaseUrl(),
      timeout: 30000,
      enableCache: true,
      cacheTimeout: 5 * 60 * 1000, // 5分钟
      retryCount: 3,
      retryDelay: 1000,
      ...config
    };

    // 创建axios实例
    this.instance = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'Accept': 'application/json',
        // 移除 Accept-Charset 请求头，因为它是浏览器禁止的不安全请求头
        // 浏览器会自动处理字符编码，无需手动设置
      },
    });

    this.setupInterceptors();
    this.loadAuthToken();
  }

  /**
   * 获取API基础URL
   */
  private getApiBaseUrl(): string {
    try {
      // 优先使用环境变量
      if (typeof window !== 'undefined') {
        const env = (window as any).__ENV__;
        if (env && env.REACT_APP_API_URL) {
          return env.REACT_APP_API_URL;
        }
      }

      // 检查环境变量
      if (process.env.VITE_API_URL) {
        return process.env.VITE_API_URL;
      }

      // 开发环境
      if (process.env.NODE_ENV === 'development') {
        return 'http://localhost:3000/api';
      }
    } catch (error) {
      console.warn('获取API URL失败，使用默认配置:', error);
    }

    // 生产环境 - 使用相对路径，通过nginx代理
    return '/api';
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 检查是否跳过认证
        const skipAuth = (config as any).skipAuth;

        // 添加认证头（除非明确跳过）
        if (!skipAuth && this.authToken && !config.headers.Authorization) {
          config.headers.Authorization = `Bearer ${this.authToken}`;
        }

        // 添加请求ID
        config.headers['X-Request-ID'] = this.generateRequestId();

        this.emit('request:start', config);
        return config;
      },
      (error) => {
        this.emit('request:error', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        this.emit('response:success', response);
        return response;
      },
      (error) => {
        this.handleResponseError(error);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 处理响应错误
   */
  private handleResponseError(error: any): void {
    const { response } = error;

    if (response) {
      switch (response.status) {
        case 401:
          // 未授权，清除token并跳转登录
          this.clearAuthToken();
          this.emit('auth:unauthorized');
          break;
        case 403:
          // 禁止访问
          this.emit('auth:forbidden');
          break;
        case 500:
          // 服务器错误
          this.emit('server:error', response.data);
          break;
        default:
          this.emit('response:error', error);
      }
    } else {
      // 网络错误
      this.emit('network:error', error);
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 加载认证token
   */
  private loadAuthToken(): void {
    if (typeof window !== 'undefined') {
      // 优先从localStorage获取，如果没有则从sessionStorage获取
      this.authToken = localStorage.getItem('token') || sessionStorage.getItem('token');
    }
  }

  /**
   * 设置认证token
   */
  public setAuthToken(token: string, remember: boolean = false): void {
    this.authToken = token;
    if (typeof window !== 'undefined') {
      if (remember) {
        // 记住我：使用localStorage，持久化存储
        localStorage.setItem('token', token);
        localStorage.setItem('rememberMe', 'true');
        sessionStorage.removeItem('token');
      } else {
        // 不记住：使用sessionStorage，会话结束后清除
        sessionStorage.setItem('token', token);
        localStorage.removeItem('rememberMe');
        localStorage.removeItem('token');
      }
    }
    this.emit('auth:token:set', token);
  }

  /**
   * 清除认证token
   */
  public clearAuthToken(): void {
    this.authToken = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      localStorage.removeItem('rememberMe');
    }
    this.emit('auth:token:clear');
  }

  /**
   * 获取缓存键
   */
  private getCacheKey(url: string, params?: any): string {
    const paramStr = params ? JSON.stringify(params) : '';
    return `${url}:${paramStr}`;
  }

  /**
   * 检查缓存
   */
  private checkCache(key: string): any | null {
    if (!this.config.enableCache) return null;

    const cached = this.cache.get(key);
    if (cached) {
      const now = Date.now();
      if (now - cached.timestamp < this.config.cacheTimeout) {
        return cached.data;
      } else {
        this.cache.delete(key);
      }
    }
    return null;
  }

  /**
   * 设置缓存
   */
  private setCache(key: string, data: any): void {
    if (this.config.enableCache) {
      this.cache.set(key, {
        data,
        timestamp: Date.now()
      });
    }
  }

  /**
   * 发送GET请求
   */
  public async get<T = any>(url: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const cacheKey = this.getCacheKey(url, options.params);
    
    // 检查缓存
    if (options.useCache !== false) {
      const cached = this.checkCache(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const response = await this.instance.get<ApiResponse<T>>(url, options);
    
    // 设置缓存
    if (options.useCache !== false) {
      this.setCache(cacheKey, response.data);
    }

    return response.data;
  }

  /**
   * 发送POST请求
   */
  public async post<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, options);
    return response.data;
  }

  /**
   * 发送PUT请求
   */
  public async put<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, options);
    return response.data;
  }

  /**
   * 发送PATCH请求
   */
  public async patch<T = any>(url: string, data?: any, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const response = await this.instance.patch<ApiResponse<T>>(url, data, options);
    return response.data;
  }

  /**
   * 发送DELETE请求
   */
  public async delete<T = any>(url: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T>>(url, options);
    return response.data;
  }

  /**
   * 上传文件
   */
  public async upload<T = any>(url: string, file: File, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    const config: AxiosRequestConfig = {
      ...options,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...options.headers,
      },
    };

    const response = await this.instance.post<ApiResponse<T>>(url, formData, config);
    return response.data;
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.cache.clear();
    this.emit('cache:cleared');
  }

  /**
   * 获取实例配置
   */
  public getConfig(): ApiConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ApiConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.instance.defaults.baseURL = this.config.baseURL;
    this.instance.defaults.timeout = this.config.timeout;
    this.emit('config:updated', this.config);
  }
}

// 创建默认实例
export const apiClient = new ApiClient();

// 导出类型
export default ApiClient;
